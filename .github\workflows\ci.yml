name: CI

on:
  pull_request:
  push:
    branches:
      - dev

jobs:
  lint-and-test:
    runs-on: ubuntu-24.04
    timeout-minutes: 10
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version-file: '.nvmrc'
      - name: Enable Corepack
        run: corepack enable
      - name: Install node_modules
        run: yarn install --immutable
      - name: Check constraints
        run: yarn constraints
      - name: Check duplication
        run: yarn dedupe --check
      - name: Prettier
        run: yarn run prettier . --check --experimental-cli
      - name: Typecheck
        run: yarn typecheck
      - name: Build
        run: yarn build
