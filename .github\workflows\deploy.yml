name: Deploy to ECS

on:
  push:
    branches:
      - dev

permissions:
  id-token: write
  contents: read

jobs:
  cd:
    name: Deploy to ECS
    runs-on: ubuntu-latest
    env:
      AWS_ACCOUNT_ID: ${{ secrets.AWS_ACCOUNT_ID }}
      AWS_IAM_ROLE_ARN: ${{ secrets.AWS_IAM_ROLE_ARN }}
      S3_BUCKET_NAME: ${{ vars.S3_BUCKET_NAME }}
      ECR_REPOSITORY_NAME: ${{ vars.ECR_REPOSITORY_NAME }}
      ECS_SERVICE_NAME: ${{ vars.ECS_SERVICE_NAME }}
      ECS_CLUSTER_NAME: ${{ vars.ECS_CLUSTER_NAME }}
      AWS_REGION: ap-northeast-1
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v1

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ env.AWS_IAM_ROLE_ARN }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Create .env file
        run: |
          cat <<EOF > .env
          VITE_ENVIRONMENT=${{ vars.VITE_ENVIRONMENT }}
          VITE_API_BASE_URL=${{ vars.VITE_API_BASE_URL }}
          VITE_API_TIMEOUT=${{ vars.VITE_API_TIMEOUT }}
          VITE_AUTH0_DOMAIN=${{ vars.VITE_AUTH0_DOMAIN }}
          VITE_AUTH0_CLIENT_ID=${{ vars.VITE_AUTH0_CLIENT_ID }}
          EOF

      - name: Log in to Amazon ECR
        run: |
          aws ecr get-login-password --region ap-northeast-1 | docker login --username AWS --password-stdin $AWS_ACCOUNT_ID.dkr.ecr.ap-northeast-1.amazonaws.com

      - name: Generate image tag
        id: image-tag
        run: echo "IMAGE_TAG=$(echo $GITHUB_SHA | cut -c1-7)" >> $GITHUB_ENV

      - name: Build, tag, and push image to Amazon ECR
        run: |
          IMAGE_TAG=$(echo $GITHUB_SHA | cut -c1-7)
          IMAGE_URI="$AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/$ECR_REPOSITORY_NAME"

          echo "🚀 Building Docker image: $IMAGE_URI:$IMAGE_TAG"

          # Build and push the image with the commit SHA tag
          docker build -f Dockerfile.dev -t $IMAGE_URI:$IMAGE_TAG .
          docker push $IMAGE_URI:$IMAGE_TAG

          # Tag the image as 'latest' and push it
          docker tag $IMAGE_URI:$IMAGE_TAG $IMAGE_URI:latest
          docker push $IMAGE_URI:latest

          echo "IMAGE_URI=$IMAGE_URI" >> $GITHUB_ENV

      - name: Clean up old images in ECR
        run: |
          echo "🧹 Cleaning up old images in ECR repository: $ECR_REPOSITORY_NAME"

          # List all images except 'latest' and delete them
          aws ecr list-images --repository-name $ECR_REPOSITORY_NAME --region $AWS_REGION \
            --query 'imageIds[?imageTag!=`latest`]' --output json | \
            jq -c '.[]' | while read image; do
              aws ecr batch-delete-image --repository-name $ECR_REPOSITORY_NAME --region $AWS_REGION \
                --image-ids "$image"
            done

      - name: Update ECS Task Definition
        run: |
          sed -i "s|<IMAGE_URI>|${IMAGE_URI}|g" ecs-task-def.dev.json

      - name: Register new ECS Task Definition
        run: |
          aws ecs register-task-definition --cli-input-json file://ecs-task-def.dev.json

      - name: Deploy to Amazon ECS
        id: deploy-ecs
        uses: aws-actions/amazon-ecs-deploy-task-definition@v2
        with:
          task-definition: ecs-task-def.dev.json
          service: ${{ env.ECS_SERVICE_NAME }}
          cluster: ${{ env.ECS_CLUSTER_NAME }}

      - name: Force ECS New Deployment
        run: |
          aws ecs update-service --cluster $ECS_CLUSTER_NAME \
            --service $ECS_SERVICE_NAME \
            --force-new-deployment
