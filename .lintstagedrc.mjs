import path from 'path'

const maxWarnings = 0

const buildEslintCommand = (filenames) =>
  `next lint --max-warnings ${maxWarnings} --file ${filenames
    .map((f) => path.relative(process.cwd(), f))
    .join(' --file ')}`

const buildStylelintCommand = (filenames) =>
  `prettier --check ${filenames.map((f) => path.relative(process.cwd(), f)).join(' ')}`

export default {
  '*.{js,jsx,ts,tsx}': [buildEslintCommand],
  '*.{js,jsx,ts,tsx,css,scss}': [buildStylelintCommand],
}
