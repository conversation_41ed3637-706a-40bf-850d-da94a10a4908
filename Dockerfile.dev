FROM node:22-slim AS build

WORKDIR /app

COPY package*.json yarn.lock* .yarnrc.yml ./
COPY .yarn .yarn

RUN corepack enable
RUN yarn install

COPY . .

RUN yarn run build

FROM nginx:stable-alpine
COPY --from=build /app/build/client /usr/share/nginx/html
COPY nginx/nginx.dev.conf /etc/nginx/nginx.conf

RUN apk add --no-cache apache2-utils
# RUN htpasswd -bc /etc/nginx/.htpasswd admin pq0CMBX5n4mpj0f

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
