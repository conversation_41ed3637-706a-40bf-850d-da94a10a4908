# React + TypeScript + Vite

This template provides a minimal setup to get <PERSON><PERSON> working in Vite with Hot Module Replacement (HMR) and some ESLint rules.

```
src/
├── assets/           # Static assets (images, fonts, etc.)
├── components/       # Reusable UI components
├── configs/         # Application configuration files
├── constants/       # Constant values and enums
├── core/           # Core utilities and services
├── guards/         # Route protection components
│   ├── AuthGuard.tsx    # Authentication guard
│   └── GuestGuard.tsx   # Guest route guard
├── helper/         # Helper functions and utilities
├── hooks/          # Custom React hooks
├── layouts/        # Page layout components
│   ├── AuthLayout.tsx   # Authentication layout
│   └── MainLayout.tsx   # Main application layout
├── example-data/           # Example data for development
├── pages/          # Page components
│   └── home/          # Home page
├── routes/         # Route definitions
├── services/       # API services and data fetching
├── store/          # Redux store configuration
├── style/          # Global styles
├── theme/          # Theme configuration
├── types/          # TypeScript type definitions
├── validations/    # Form validation schemas
├── App.tsx         # Root application component
├── index.css       # Global CSS
├── main.tsx        # Application entry point
└── vite-env.d.ts   # Vite environment type definitions
```

- **React**: A JavaScript library for building user interfaces.
- **TypeScript**: A superset of JavaScript that adds static types.
- **Vite**: A fast build tool that provides a modern development experience.
- **ESLint**: A tool for identifying and fixing problems in JavaScript code.

# Yarn Installation and Usage Guide

## Installing Yarn

1. Use a node version manager that supports .nvmrc, such as [nvm](https://github.com/nvm-sh/nvm), [n](https://github.com/tj/n), [fnm](https://github.com/Schniz/fnm), or [asdf](https://github.com/asdf-vm/asdf).
2. Install the required Node.js version specified in `.nvmrc`.
3. Run the following command to enable Yarn.
   ```shell
   corepack enable
   ```

## Verify Installation

```bash
yarn --version
```

## Getting Started

To get started with this project, follow these steps:

1. **Clone the repository**:

   ```bash
   <NAME_EMAIL>:jmdc-inc/kensuke-plus-client.git
   cd kensuke-plus-client
   ```

2. **Install dependencies**:

   ```bash
   yarn
   ```

3. **Copy environment variables**:

   ```bash
   cp env.example .env
   ```

4. **Run the development server**:

   ```bash
   yarn dev
   ```

5. **Build for production**:

   ```bash
   yarn build
   ```

6. **Preview the production build**:

   ```bash
   yarn preview
   ```

## Running with Docker

To run this project using Docker, follow these steps:

**Run the development server with Docker Compose**:

```bash
docker-compose -f docker-compose.local.yaml up --build
```
