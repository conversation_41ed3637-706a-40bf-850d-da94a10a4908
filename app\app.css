@tailwind base;
@tailwind components;
@tailwind utilities;

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family:
    'Helvetica Neue', '<PERSON><PERSON>', 'Hiragino Kaku Gothic ProN', 'Hiragino Sans',
    '<PERSON><PERSON>', sans-serif !important;
}

body {
  color: #1a1a1a;
}

::-webkit-scrollbar {
  width: 12px;
  height: 12px;
}

::-webkit-scrollbar-track {
  margin: 16px;
  background: #e6e6e6;
  border-radius: 8px;
}

::-webkit-scrollbar-thumb {
  background: #999999;
  border-radius: 8px;
}

.loader {
  border: 4px solid #666666;
  border-top: 4px solid #3460fb;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  animation: spin 1s linear infinite;
}

.MuiMenu-list {
  scroll-behavior: auto !important;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

input[type='checkbox'] {
  appearance: none;
  -webkit-appearance: none;
  width: 16px;
  height: 16px;
  border: 1px solid #666666;
  border-radius: 30%;
  background-color: white;
  cursor: pointer;
  display: inline-block;
  position: relative;
}

input[type='checkbox']:checked {
  background-color: #068667;
  border-color: #068667;
}

input[type='checkbox']:checked::after {
  content: '';
  position: absolute;
  top: 43%;
  left: 48%;
  transform: translate(-50%, -50%) rotate(45deg);
  width: 5px;
  height: 8px;
  border: solid white;
  border-width: 0 2px 2px 0;
}

.status-box {
  position: relative;
  clip-path: polygon(
    0 0,
    calc(100% - 10px) 0,
    100% 50%,
    calc(100% - 10px) 100%,
    0 100%
  );
}

.status-box::after {
  content: '';
  position: absolute;
  bottom: 0;
  width: 100%;
  height: 10px;
  border-radius: 2px;
}

.status-box:not(:first-child) {
  clip-path: polygon(
    0 0,
    calc(100% - 15px) 0,
    100% 50%,
    calc(100% - 15px) 100%,
    0 100%,
    10px 50%
  );
}
