import { Link } from 'react-router'
import { Breadcrumbs } from './MaterialUI'

type LinkItem = {
  label: string
  to?: string
  isCurrent?: boolean
}

type BreadcrumbsComponentProps = {
  links: LinkItem[]
}

export const BreadcrumbsComponent: React.FC<BreadcrumbsComponentProps> = ({
  links,
}) => {
  return (
    <Breadcrumbs
      separator={<span className={'text-gray-900'}> {'>'} </span>}
      aria-label={'breadcrumb'}
    >
      {links.map((link) => {
        const isCurrent = link.isCurrent ?? false
        return isCurrent ?
            <div className={'text-sm text-gray-900'} key={link.label}>
              {link.label}
            </div>
          : <Link key={link.label} to={link.to ?? '#'}>
              <span
                className={
                  'text-sm text-gray-900 underline hover:text-green-700'
                }
              >
                {link.label}
              </span>
            </Link>
      })}
    </Breadcrumbs>
  )
}
