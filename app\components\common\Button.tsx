import clsx from 'clsx'

type ButtonProps = {
  className?: string
  type?: 'button' | 'submit' | 'reset'
  onClick?: () => void
  children?: React.ReactNode
}

const Button = ({
  children,
  type = 'button',
  className,
  onClick,
  ...rest
}: ButtonProps) => {
  return (
    <button
      type={type}
      className={clsx(
        'w-[200px] rounded-md bg-green-600 px-[24px] py-[12px] text-lg font-semibold text-white hover:bg-green-700 focus:outline-none',
        className,
      )}
      onClick={onClick}
      {...rest}
    >
      {children}
    </button>
  )
}

export default Button
