import { useState } from 'react'
import { MdOutlineFileDownload } from 'react-icons/md'
import { EXPORT_TYPES } from '../../constants'
import TITLE from '../../constants/title'
import { Box, Popover } from './MaterialUI'

type ExportButtonProps = {
  disabled?: boolean
  onExport?: (type: string) => void
  excludeExport?: string[]
}

const ExportButton: React.FC<ExportButtonProps> = ({
  disabled,
  onExport,
  excludeExport = [],
}) => {
  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null)
  const open = Boolean(anchorEl)
  const id = open ? 'export-popover' : undefined

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget)
  }

  const handleClose = () => {
    setAnchorEl(null)
  }

  const handleExportSelect = (value: string) => {
    handleClose()
    onExport?.(value)
  }

  return (
    <div className={'flex items-center'}>
      <Box
        component={'button'}
        onClick={handleClick}
        disabled={disabled}
        className={`flex items-center justify-between gap-1 border-2 ${disabled ? 'cursor-not-allowed border-gray-400 text-gray-400' : 'border-green-600 text-green-600 hover:border-green-900 hover:text-green-900'} h-[32px] w-[64px] rounded-lg text-xs font-semibold transition-all`}
      >
        <MdOutlineFileDownload className={'ml-1 text-xl'} />
        <span className={'mr-2 whitespace-nowrap text-xs-bold'}>
          {TITLE.button.export}
        </span>
      </Box>
      <Popover
        id={id}
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
      >
        <div className={'cursor-pointer p-2 pr-10 hover:bg-gray-100'}>
          <button
            onClick={() => handleExportSelect(EXPORT_TYPES.PDF)}
            className={'w-full whitespace-nowrap text-left'}
          >
            PDF
          </button>
        </div>
        {excludeExport?.includes(EXPORT_TYPES.CSV) ? null : (
          <div className={'cursor-pointer p-2 pr-10 hover:bg-gray-100'}>
            <button
              onClick={() => handleExportSelect(EXPORT_TYPES.CSV)}
              className={'w-full whitespace-nowrap text-left'}
            >
              CSV
            </button>
          </div>
        )}
        {excludeExport?.includes(EXPORT_TYPES.BID) ? null : (
          <div className={'cursor-pointer p-2 pr-10 hover:bg-gray-100'}>
            <button
              onClick={() => handleExportSelect(EXPORT_TYPES.BID)}
              className={'w-full whitespace-nowrap text-left'}
            >
              BID CSV
            </button>
          </div>
        )}
      </Popover>
    </div>
  )
}

export default ExportButton
