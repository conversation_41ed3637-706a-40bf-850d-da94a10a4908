import React, { useState } from 'react'
import { PERIOD } from '../../constants'
import TITLE from '../../constants/title'
import ExportButton from './ExportButton'
import { Box } from './MaterialUI'
import RadioCustom from './RadioCustom'
import SelectCustom from './SelectCustom'
import TransferListModal from './TransferList'
import VerticalDivider from './VerticalDivider'

type PeriodType = (typeof PERIOD)[keyof typeof PERIOD] // 'month' | 'year'
type FilterByYearProps = {
  hasFilterRadio?: boolean
  notHasAgeGroup?: boolean
  notHasInsurance?: boolean
  notHasYear?: boolean
  hasDisease?: boolean
  hasDiabetes?: boolean
  hasCancer?: boolean
  notHasModal?: boolean
  handleSegmentChange?: (value: PeriodType) => void
  selectedPeriod?: PeriodType
}

const FilterByYear: React.FC<FilterByYearProps> = ({
  hasFilterRadio = false,
  notHasAgeGroup = false,
  notHasInsurance = false,
  notHasYear = false,
  hasDisease = false,
  hasDiabetes = false,
  hasCancer = false,
  notHasModal = false,
  handleSegmentChange,
  selectedPeriod,
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [rightListCount, setRightListCount] = useState(0)
  const [selectedInsurance, setSelectedInsurance] = useState<
    (string | number)[]
  >([])
  const [selectedAgeGroup, setSelectedAgeGroup] = useState<(string | number)[]>(
    [],
  )
  const [selectedYear, setSelectedYear] = useState<string | number>('')
  const [selectedDiabetes, setSelectedDiabetes] = useState<string | number>('')
  const [selectedCancer, setSelectedCancer] = useState<number>(0)
  const handleOpenModal = () => {
    setIsModalOpen(true)
  }

  const handleInsuranceChange = (
    value: string | number | (string | number)[],
  ) => {
    setSelectedInsurance(value as (string | number)[])
  }

  const handleAgeGroupChange = (
    value: string | number | (string | number)[],
  ) => {
    setSelectedAgeGroup(value as (string | number)[])
  }

  const handleYearChange = (value: string | number | (string | number)[]) => {
    setSelectedYear(value as string | number)
  }

  const handleYearDiabetes = (value: string | number | (string | number)[]) => {
    setSelectedDiabetes(value as string | number)
  }

  const handleYearCancer = (value: string | number | (string | number)[]) => {
    setSelectedCancer(value as number)
  }

  return (
    <>
      <Box className={'mt-6 flex items-center gap-6'}>
        {!notHasModal && (
          <>
            <button
              className={
                'h-[28px] w-[110px] rounded-lg border border-gray-300 text-sm hover:border-black'
              }
              onClick={handleOpenModal}
            >
              記号・所属 {rightListCount > 0 ? `(${rightListCount})` : ''}
            </button>
            <VerticalDivider />
          </>
        )}

        {!notHasYear && (
          <SelectCustom
            labelId={'year-label'}
            defaultValue={selectedYear}
            defaultLabel={'2024年度'}
            menuItems={[
              { value: 2024, label: '2024年度' },
              { value: 2023, label: '2023年度' },
              { value: 2022, label: '2022年度' },
              { value: 2021, label: '2021年度' },
              { value: 2020, label: '2020年度' },
              { value: 2019, label: '2019年度' },
              { value: 2018, label: '2018年度' },
              { value: 2017, label: '2017年度' },
              { value: 2016, label: '2016年度' },
              { value: 2015, label: '2015年度' },
              { value: 2014, label: '2014年度' },
              { value: 2013, label: '2013年度' },
              { value: 2012, label: '2012年度' },
            ]}
            width={'106px'}
            onChange={handleYearChange}
          />
        )}

        {hasFilterRadio && (
          <Box className={'flex items-center gap-4'}>
            <RadioCustom
              size={'small'}
              label={TITLE.filterByYear.year}
              checked={selectedPeriod === PERIOD.YEAR}
              onChange={() => handleSegmentChange?.(PERIOD.YEAR)}
            />
            <RadioCustom
              size={'small'}
              label={TITLE.filterByYear.month}
              checked={selectedPeriod === PERIOD.MONTH}
              onChange={() => handleSegmentChange?.(PERIOD.MONTH)}
            />
          </Box>
        )}

        {!notHasAgeGroup && (
          <>
            <VerticalDivider />
            <SelectCustom
              labelId={'age-label'}
              defaultValue={selectedAgeGroup}
              defaultLabel={'年齢層'}
              menuItems={[
                { value: 0, label: 'すべて' },
                { value: 1, label: '0 - 4歳' },
                { value: 2, label: '5 - 9歳' },
                { value: 3, label: '10 - 14歳' },
                { value: 4, label: '15 - 19歳' },
                { value: 5, label: '20 - 24歳' },
                { value: 6, label: '25 - 29歳' },
                { value: 7, label: '30 - 34歳' },
                { value: 8, label: '35 - 39歳' },
                { value: 9, label: '40 - 44歳' },
                { value: 10, label: '45 - 49歳' },
                { value: 11, label: '50 - 54歳' },
                { value: 12, label: '55 - 59歳' },
                { value: 13, label: '60 - 64歳' },
                { value: 14, label: '65 - 69歳' },
                { value: 15, label: '70 - 74歳' },
                { value: 16, label: '75 - 79歳' },
                { value: 17, label: '80 - 84歳' },
                { value: 18, label: '85 - 89歳' },
                { value: 19, label: '90 - 94歳' },
                { value: 20, label: '95 - 99歳' },
                { value: 21, label: '100歳以上' },
              ]}
              width={'82px'}
              multiple={true}
              onChange={handleAgeGroupChange}
            />
          </>
        )}

        {hasDiabetes && (
          <>
            <VerticalDivider />
            <SelectCustom
              labelId={'diabetes-label'}
              defaultValue={selectedDiabetes}
              menuItems={[
                { value: '', label: '2型糖尿病' },
                { value: 1, label: '高血圧症（本能性）' },
                { value: 2, label: '脂質異常症' },
                { value: 3, label: '肝疾患' },
                { value: 4, label: '2型糖尿病合併症' },
                { value: 5, label: '脂質異常症' },
                { value: 6, label: '虚血性心疾患' },
                { value: 7, label: '脳血管疾患' },
                { value: 8, label: '慢性腎臓病' },
              ]}
              width={'106px'}
              onChange={handleYearDiabetes}
            />
          </>
        )}

        {hasCancer && (
          <>
            <VerticalDivider />
            <SelectCustom
              labelId={'cancer-label'}
              defaultValue={selectedCancer}
              menuItems={[
                { value: 0, label: '胃がん' },
                { value: 1, label: '肺がん' },
                { value: 2, label: '大腸がん' },
                { value: 3, label: '乳がん' },
                { value: 4, label: '子宮頸がん（女性のみ）' },
                { value: 5, label: 'その他女性生殖器（女性のみ）' },
                { value: 6, label: '前立腺がん（男性のみ）' },
                { value: 7, label: 'そのほか悪性腫瘍' },
              ]}
              width={'106px'}
              onChange={handleYearCancer}
            />
          </>
        )}

        {!notHasInsurance && (
          <>
            <VerticalDivider />
            <SelectCustom
              labelId={'insurance-label'}
              defaultValue={selectedInsurance}
              defaultLabel={'加入者区分'}
              menuItems={[
                { value: 0, label: 'すべて' },
                { value: 1, label: '被保険者 男性' },
                { value: 2, label: '被保険者 女性' },
                { value: 3, label: '被扶養者 男性' },
                { value: 4, label: '被扶養者 女性' },
              ]}
              width={'166px'}
              multiple={true}
              onChange={handleInsuranceChange}
            />
          </>
        )}

        {hasDisease && (
          <SelectCustom
            labelId={'disease-label'}
            defaultValue={''}
            menuItems={[
              { value: '', label: '疾病' },
              { value: 1, label: '疾病 1' },
              { value: 2, label: '疾病 2' },
            ]}
            width={'106px'}
          />
        )}
        <Box className={'ml-auto flex'}>
          <ExportButton />
        </Box>
      </Box>
      <TransferListModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onRightListChange={setRightListCount}
      />
    </>
  )
}

export default FilterByYear
