import React from 'react'
import { BreadcrumbsComponent } from './Breadcrumbs'
import VerticalDivider from './VerticalDivider'

type BreadcrumbLink = {
  label: string
  to?: string
  isCurrent?: boolean
}

type HeaderProps = {
  breadcrumbLinks: BreadcrumbLink[]
  firstTitle: string
  secondTitle?: string
  icon: React.ReactNode
}

const HeaderSection: React.FC<HeaderProps> = ({
  breadcrumbLinks,
  firstTitle,
  secondTitle,
  icon,
}) => {
  return (
    <div>
      <BreadcrumbsComponent links={breadcrumbLinks} />
      <div className={'relative mt-8 flex items-center'}>
        <div>{icon}</div>
        <div className={'ml-2 text-xl-bold'}>{firstTitle}</div>

        {secondTitle && (
          <>
            <div className={'ml-4'}>
              <VerticalDivider />
            </div>
            <div className={'ml-[15px] text-lg-bold'}>{secondTitle}</div>
          </>
        )}
      </div>
    </div>
  )
}

export default HeaderSection
