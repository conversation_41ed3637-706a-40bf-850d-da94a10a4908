import React from 'react'
import variables from '../../theme/variables'

export type IconProps = React.SVGProps<SVGSVGElement> & {
  width?: string
  height?: string
  fill?: string
  viewBox?: string
}

export const iconSize = {
  small: '16',
  extraSmall: '18',
  smallMedium: '20',
  medium: '24',
  large: '32',
  extraLarge: '36',
}

const BaseIcon = ({
  fill = variables.green600,
  width = iconSize.extraLarge,
  height = iconSize.extraLarge,
  viewBox = '0 0 37 40',
  paths,
  ...props
}: React.SVGProps<SVGSVGElement> & {
  fill?: string
  width?: string
  height?: string
  viewBox?: string
  paths: string[]
}) => (
  <svg width={width} height={height} viewBox={viewBox} {...props}>
    {paths.map((d, index) => (
      <path key={index + 1} d={d} fill={fill} />
    ))}
  </svg>
)

export const HomeIcon = ({
  width = iconSize.medium,
  height = iconSize.medium,
  fill = '#1A1A1A',
  viewBox = '0 0 24 24',
  ...props
}: IconProps) => (
  <BaseIcon
    paths={[
      'M6 19H9V14C9 13.7167 9.09583 13.4792 9.2875 13.2875C9.47917 13.0958 9.71667 13 10 13H14C14.2833 13 14.5208 13.0958 14.7125 13.2875C14.9042 13.4792 15 13.7167 15 14V19H18V10L12 5.5L6 10V19ZM4 19V10C4 9.68333 4.07083 9.38333 4.2125 9.1C4.35417 8.81667 4.55 8.58333 4.8 8.4L10.8 3.9C11.15 3.63333 11.55 3.5 12 3.5C12.45 3.5 12.85 3.63333 13.2 3.9L19.2 8.4C19.45 8.58333 19.6458 8.81667 19.7875 9.1C19.9292 9.38333 20 9.68333 20 10V19C20 19.55 19.8042 20.0208 19.4125 20.4125C19.0208 20.8042 18.55 21 18 21H14C13.7167 21 13.4792 20.9042 13.2875 20.7125C13.0958 20.5208 13 20.2833 13 20V15H11V20C11 20.2833 10.9042 20.5208 10.7125 20.7125C10.5208 20.9042 10.2833 21 10 21H6C5.45 21 4.97917 20.8042 4.5875 20.4125C4.19583 20.0208 4 19.55 4 19Z',
    ]}
    width={width}
    height={height}
    fill={fill}
    viewBox={viewBox}
    {...props}
  />
)

export const QuickReference = ({
  width,
  height,
  fill,
  viewBox = '0 0 40 40',
  ...props
}: IconProps) => (
  <BaseIcon
    paths={[
      'M7.83203 6V15.0375V15V30V6ZM12.332 21H15.332C15.757 21 16.1133 20.8562 16.4008 20.5687C16.6883 20.2812 16.832 19.925 16.832 19.5C16.832 19.075 16.6883 18.7187 16.4008 18.4312C16.1133 18.1437 15.757 18 15.332 18H12.332C11.907 18 11.5508 18.1437 11.2633 18.4312C10.9758 18.7187 10.832 19.075 10.832 19.5C10.832 19.925 10.9758 20.2812 11.2633 20.5687C11.5508 20.8562 11.907 21 12.332 21ZM12.332 27H13.832C14.257 27 14.6133 26.8562 14.9008 26.5687C15.1883 26.2812 15.332 25.925 15.332 25.5C15.332 25.075 15.1883 24.7187 14.9008 24.4312C14.6133 24.1437 14.257 24 13.832 24H12.332C11.907 24 11.5508 24.1437 11.2633 24.4312C10.9758 24.7187 10.832 25.075 10.832 25.5C10.832 25.925 10.9758 26.2812 11.2633 26.5687C11.5508 26.8562 11.907 27 12.332 27ZM7.83203 33C7.00703 33 6.30078 32.7062 5.71328 32.1187C5.12578 31.5312 4.83203 30.825 4.83203 30V6C4.83203 5.175 5.12578 4.46875 5.71328 3.88125C6.30078 3.29375 7.00703 3 7.83203 3H18.5945C18.9945 3 19.3758 3.075 19.7383 3.225C20.1008 3.375 20.4195 3.5875 20.6945 3.8625L27.9695 11.1375C28.2445 11.4125 28.457 11.7312 28.607 12.0937C28.757 12.4562 28.832 12.8375 28.832 13.2375V14.2875C28.832 14.7125 28.6883 15.0625 28.4008 15.3375C28.1133 15.6125 27.757 15.75 27.332 15.75C26.907 15.75 26.5508 15.6062 26.2633 15.3187C25.9758 15.0312 25.832 14.675 25.832 14.25V13.5H19.832C19.407 13.5 19.0508 13.3562 18.7633 13.0687C18.4758 12.7812 18.332 12.425 18.332 12V6H7.83203V30H16.082C16.507 30 16.8633 30.1437 17.1508 30.4312C17.4383 30.7187 17.582 31.075 17.582 31.5C17.582 31.925 17.4383 32.2812 17.1508 32.5687C16.8633 32.8562 16.507 33 16.082 33H7.83203ZM25.082 28.5C26.132 28.5 27.0195 28.1375 27.7445 27.4125C28.4695 26.6875 28.832 25.8 28.832 24.75C28.832 23.7 28.4695 22.8125 27.7445 22.0875C27.0195 21.3625 26.132 21 25.082 21C24.032 21 23.1445 21.3625 22.4195 22.0875C21.6945 22.8125 21.332 23.7 21.332 24.75C21.332 25.8 21.6945 26.6875 22.4195 27.4125C23.1445 28.1375 24.032 28.5 25.082 28.5ZM33.782 33.45C33.507 33.725 33.157 33.8625 32.732 33.8625C32.307 33.8625 31.957 33.725 31.682 33.45L28.682 30.45C28.157 30.8 27.5883 31.0625 26.9758 31.2375C26.3633 31.4125 25.732 31.5 25.082 31.5C23.207 31.5 21.6133 30.8437 20.3008 29.5312C18.9883 28.2187 18.332 26.625 18.332 24.75C18.332 22.875 18.9883 21.2812 20.3008 19.9687C21.6133 18.6562 23.207 18 25.082 18C26.957 18 28.5508 18.6562 29.8633 19.9687C31.1758 21.2812 31.832 22.875 31.832 24.75C31.832 25.4 31.7445 26.0312 31.5695 26.6437C31.3945 27.2562 31.132 27.825 30.782 28.35L33.782 31.35C34.057 31.625 34.1945 31.975 34.1945 32.4C34.1945 32.825 34.057 33.175 33.782 33.45Z',
    ]}
    width={width}
    height={height}
    fill={fill}
    viewBox={viewBox}
    {...props}
  />
)

export const StethoscopeSearch = ({
  width,
  height,
  fill,
  viewBox = '0 0 36 30',
  ...props
}: IconProps) => (
  <BaseIcon
    paths={[
      'M17 30C14.425 30 12.2813 29.0375 10.5688 27.1125C8.85625 25.1875 8 22.9 8 20.25V19.3875C5.85 19.0375 4.0625 18.0312 2.6375 16.3688C1.2125 14.7063 0.5 12.75 0.5 10.5V3C0.5 2.575 0.64375 2.21875 0.93125 1.93125C1.21875 1.64375 1.575 1.5 2 1.5H5C5 1.075 5.14375 0.71875 5.43125 0.43125C5.71875 0.14375 6.075 0 6.5 0C6.925 0 7.28125 0.14375 7.56875 0.43125C7.85625 0.71875 8 1.075 8 1.5V4.5C8 4.925 7.85625 5.28125 7.56875 5.56875C7.28125 5.85625 6.925 6 6.5 6C6.075 6 5.71875 5.85625 5.43125 5.56875C5.14375 5.28125 5 4.925 5 4.5H3.5V10.5C3.5 12.15 4.0875 13.5625 5.2625 14.7375C6.4375 15.9125 7.85 16.5 9.5 16.5C11.15 16.5 12.5625 15.9125 13.7375 14.7375C14.9125 13.5625 15.5 12.15 15.5 10.5V4.5H14C14 4.925 13.8563 5.28125 13.5688 5.56875C13.2813 5.85625 12.925 6 12.5 6C12.075 6 11.7188 5.85625 11.4313 5.56875C11.1438 5.28125 11 4.925 11 4.5V1.5C11 1.075 11.1438 0.71875 11.4313 0.43125C11.7188 0.14375 12.075 0 12.5 0C12.925 0 13.2813 0.14375 13.5688 0.43125C13.8563 0.71875 14 1.075 14 1.5H17C17.425 1.5 17.7813 1.64375 18.0688 1.93125C18.3563 2.21875 18.5 2.575 18.5 3V10.5C18.5 12.75 17.7875 14.7063 16.3625 16.3688C14.9375 18.0312 13.15 19.0375 11 19.3875V20.25C11 22.05 11.5688 23.625 12.7063 24.975C13.8438 26.325 15.275 27 17 27C17.425 27 17.7813 27.1437 18.0688 27.4313C18.3563 27.7188 18.5 28.075 18.5 28.5C18.5 28.925 18.3563 29.2812 18.0688 29.5687C17.7813 29.8563 17.425 30 17 30Z',
      'M23.75 24C24.8 24 25.6875 23.6375 26.4125 22.9125C27.1375 22.1875 27.5 21.3 27.5 20.25C27.5 19.2 27.1375 18.3125 26.4125 17.5875C25.6875 16.8625 24.8 16.5 23.75 16.5C22.7 16.5 21.8125 16.8625 21.0875 17.5875C20.3625 18.3125 20 19.2 20 20.25C20 21.3 20.3625 22.1875 21.0875 22.9125C21.8125 23.6375 22.7 24 23.75 24ZM23.75 27C21.875 27 20.2813 26.3438 18.9688 25.0313C17.6563 23.7188 17 22.125 17 20.25C17 18.375 17.6563 16.7812 18.9688 15.4688C20.2813 14.1562 21.875 13.5 23.75 13.5C25.625 13.5 27.2188 14.1562 28.5313 15.4688C29.8438 16.7812 30.5 18.375 30.5 20.25C30.5 20.9 30.4125 21.5313 30.2375 22.1438C30.0625 22.7563 29.8 23.325 29.45 23.85L32.45 26.85C32.725 27.125 32.8625 27.475 32.8625 27.9C32.8625 28.325 32.725 28.675 32.45 28.95C32.175 29.225 31.825 29.3625 31.4 29.3625C30.975 29.3625 30.625 29.225 30.35 28.95L27.35 25.95C26.825 26.3 26.2563 26.5625 25.6438 26.7375C25.0313 26.9125 24.4 27 23.75 27Z',
    ]}
    width={width}
    height={height}
    fill={fill}
    viewBox={viewBox}
    {...props}
  />
)

export const GroupSearch = ({
  width,
  height,
  fill,
  viewBox = '0 0 37 36',
  ...props
}: IconProps) => (
  <BaseIcon
    paths={[
      'M5.16406 30C4.33906 30 3.63281 29.7063 3.04531 29.1188C2.45781 28.5313 2.16406 27.825 2.16406 27V25.8C2.16406 24.95 2.38281 24.1688 2.82031 23.4563C3.25781 22.7438 3.83906 22.2 4.56406 21.825C6.11406 21.05 7.68906 20.4688 9.28906 20.0813C10.8891 19.6938 12.5141 19.5 14.1641 19.5C14.7391 19.5 15.3078 19.525 15.8703 19.575C16.4328 19.625 17.0016 19.7 17.5766 19.8C17.9266 19.85 18.1766 20.0438 18.3266 20.3813C18.4766 20.7188 18.4891 21.0625 18.3641 21.4125C18.2391 21.7625 18.0016 22.0688 17.6516 22.3313C17.3016 22.5938 16.8016 22.6875 16.1516 22.6125C15.8016 22.5625 15.4641 22.5313 15.1391 22.5188C14.8141 22.5063 14.4891 22.5 14.1641 22.5C12.7641 22.5 11.3766 22.6688 10.0016 23.0063C8.62656 23.3438 7.26406 23.85 5.91406 24.525C5.68906 24.65 5.50781 24.825 5.37031 25.05C5.23281 25.275 5.16406 25.525 5.16406 25.8V27H16.2266C16.7266 27 17.1016 27.1563 17.3516 27.4688C17.6016 27.7813 17.7266 28.125 17.7266 28.5C17.7266 28.875 17.6016 29.2188 17.3516 29.5313C17.1016 29.8438 16.7266 30 16.2266 30H5.16406ZM14.1641 18C12.5141 18 11.1016 17.4125 9.92656 16.2375C8.75156 15.0625 8.16406 13.65 8.16406 12C8.16406 10.35 8.75156 8.9375 9.92656 7.7625C11.1016 6.5875 12.5141 6 14.1641 6C15.8141 6 17.2266 6.5875 18.4016 7.7625C19.5766 8.9375 20.1641 10.35 20.1641 12C20.1641 13.65 19.5766 15.0625 18.4016 16.2375C17.2266 17.4125 15.8141 18 14.1641 18ZM29.1641 12C29.1641 13.65 28.5766 15.0625 27.4016 16.2375C26.2266 17.4125 24.8141 18 23.1641 18C22.8891 18 22.5391 17.9688 22.1141 17.9063C21.6891 17.8438 21.3391 17.775 21.0641 17.7C21.7391 16.9 22.2578 16.0125 22.6203 15.0375C22.9828 14.0625 23.1641 13.05 23.1641 12C23.1641 10.95 22.9828 9.9375 22.6203 8.9625C22.2578 7.9875 21.7391 7.1 21.0641 6.3C21.4141 6.175 21.7641 6.09375 22.1141 6.05625C22.4641 6.01875 22.8141 6 23.1641 6C24.8141 6 26.2266 6.5875 27.4016 7.7625C28.5766 8.9375 29.1641 10.35 29.1641 12ZM14.1641 15C14.9891 15 15.6953 14.7063 16.2828 14.1188C16.8703 13.5313 17.1641 12.825 17.1641 12C17.1641 11.175 16.8703 10.4688 16.2828 9.88125C15.6953 9.29375 14.9891 9 14.1641 9C13.3391 9 12.6328 9.29375 12.0453 9.88125C11.4578 10.4688 11.1641 11.175 11.1641 12C11.1641 12.825 11.4578 13.5313 12.0453 14.1188C12.6328 14.7063 13.3391 15 14.1641 15ZM26.1641 28.5C27.0141 28.5 27.7203 28.25 28.2828 27.75C28.8453 27.25 29.1391 26.5 29.1641 25.5C29.1891 24.65 28.9078 23.9375 28.3203 23.3625C27.7328 22.7875 27.0141 22.5 26.1641 22.5C25.3141 22.5 24.6016 22.7875 24.0266 23.3625C23.4516 23.9375 23.1641 24.65 23.1641 25.5C23.1641 26.35 23.4516 27.0625 24.0266 27.6375C24.6016 28.2125 25.3141 28.5 26.1641 28.5ZM26.1641 31.5C24.5141 31.5 23.1016 30.9125 21.9266 29.7375C20.7516 28.5625 20.1641 27.15 20.1641 25.5C20.1641 23.85 20.7516 22.4375 21.9266 21.2625C23.1016 20.0875 24.5141 19.5 26.1641 19.5C27.8141 19.5 29.2266 20.0875 30.4016 21.2625C31.5766 22.4375 32.1641 23.85 32.1641 25.5C32.1641 26.075 32.0953 26.6188 31.9578 27.1313C31.8203 27.6438 31.6141 28.125 31.3391 28.575L34.1141 31.35C34.3891 31.625 34.5266 31.975 34.5266 32.4C34.5266 32.825 34.3891 33.175 34.1141 33.45C33.8391 33.725 33.4891 33.8625 33.0641 33.8625C32.6391 33.8625 32.2891 33.725 32.0141 33.45L29.2391 30.675C28.7891 30.95 28.3078 31.1563 27.7953 31.2938C27.2828 31.4313 26.7391 31.5 26.1641 31.5Z',
    ]}
    width={width}
    height={height}
    fill={fill}
    viewBox={viewBox}
    {...props}
  />
)

export const ReadinessScore = ({
  width = iconSize.large,
  height = iconSize.large,
  fill,
  viewBox = '0 0 30 25',
  ...props
}: IconProps) => (
  <BaseIcon
    paths={[
      'M7.16667 24.0333C6.36667 24.0111 5.60556 23.8056 4.88333 23.4167C4.16111 23.0278 3.47778 22.4556 2.83333 21.7C1.94444 20.6333 1.25 19.3611 0.75 17.8833C0.25 16.4056 0 14.8889 0 13.3333C0 11.4889 0.35 9.75556 1.05 8.13333C1.75 6.51111 2.7 5.1 3.9 3.9C5.1 2.7 6.51111 1.75 8.13333 1.05C9.75556 0.35 11.4889 0 13.3333 0C15.1778 0 16.9111 0.355556 18.5333 1.06667C20.1556 1.77778 21.5667 2.74444 22.7667 3.96667C23.9667 5.18889 24.9167 6.62222 25.6167 8.26667C26.3167 9.91111 26.6667 11.6778 26.6667 13.5667C26.6667 15.2778 26.3889 16.8778 25.8333 18.3667C25.2778 19.8556 24.4889 21.1111 23.4667 22.1333C22.8444 22.7556 22.1889 23.2278 21.5 23.55C20.8111 23.8722 20.1111 24.0333 19.4 24.0333C19 24.0333 18.6 23.9833 18.2 23.8833C17.8 23.7833 17.4 23.6333 17 23.4333L15.1333 22.5C14.8667 22.3667 14.5833 22.2667 14.2833 22.2C13.9833 22.1333 13.6667 22.1 13.3333 22.1C13 22.1 12.6833 22.1333 12.3833 22.2C12.0833 22.2667 11.8 22.3667 11.5333 22.5L9.66667 23.4333C9.24444 23.6556 8.82778 23.8167 8.41667 23.9167C8.00556 24.0167 7.58889 24.0556 7.16667 24.0333ZM7.23333 21.3667C7.43333 21.3667 7.63889 21.3444 7.85 21.3C8.06111 21.2556 8.26667 21.1778 8.46667 21.0667L10.3333 20.1333C10.8 19.8889 11.2833 19.7111 11.7833 19.6C12.2833 19.4889 12.7889 19.4333 13.3 19.4333C13.8111 19.4333 14.3222 19.4889 14.8333 19.6C15.3444 19.7111 15.8333 19.8889 16.3 20.1333L18.2 21.0667C18.4 21.1778 18.6 21.2556 18.8 21.3C19 21.3444 19.2 21.3667 19.4 21.3667C19.8222 21.3667 20.2222 21.2556 20.6 21.0333C20.9778 20.8111 21.3556 20.4778 21.7333 20.0333C22.4444 19.1889 23 18.1778 23.4 17C23.8 15.8222 24 14.6111 24 13.3667C24 10.3889 22.9667 7.86111 20.9 5.78333C18.8333 3.70556 16.3111 2.66667 13.3333 2.66667C10.3556 2.66667 7.83333 3.71111 5.76667 5.8C3.7 7.88889 2.66667 10.4222 2.66667 13.4C2.66667 14.6667 2.87222 15.9 3.28333 17.1C3.69444 18.3 4.26667 19.3111 5 20.1333C5.37778 20.5778 5.74444 20.8944 6.1 21.0833C6.45556 21.2722 6.83333 21.3667 7.23333 21.3667ZM13.3333 16C14.0667 16 14.6944 15.7389 15.2167 15.2167C15.7389 14.6944 16 14.0667 16 13.3333C16 13.1556 15.9833 12.9778 15.95 12.8C15.9167 12.6222 15.8667 12.4444 15.8 12.2667L17.4667 10.0333C17.7111 10.3444 17.9111 10.6611 18.0667 10.9833C18.2222 11.3056 18.3556 11.6444 18.4667 12C18.5778 12.3556 18.7444 12.6667 18.9667 12.9333C19.1889 13.2 19.4778 13.3333 19.8333 13.3333C20.2778 13.3333 20.6278 13.1389 20.8833 12.75C21.1389 12.3611 21.2111 11.9333 21.1 11.4667C20.6556 9.66667 19.7111 8.19444 18.2667 7.05C16.8222 5.90556 15.1778 5.33333 13.3333 5.33333C11.4667 5.33333 9.81667 5.90556 8.38333 7.05C6.95 8.19444 6.01111 9.66667 5.56667 11.4667C5.45556 11.9333 5.52778 12.3611 5.78333 12.75C6.03889 13.1389 6.38889 13.3333 6.83333 13.3333C7.18889 13.3333 7.47778 13.2 7.7 12.9333C7.92222 12.6667 8.08889 12.3556 8.2 12C8.51111 10.8222 9.13889 9.86111 10.0833 9.11667C11.0278 8.37222 12.1111 8 13.3333 8C13.6889 8 14.0389 8.03333 14.3833 8.1C14.7278 8.16667 15.0556 8.26667 15.3667 8.4L13.6667 10.7C13.6222 10.7 13.5667 10.6944 13.5 10.6833C13.4333 10.6722 13.3778 10.6667 13.3333 10.6667C12.6 10.6667 11.9722 10.9278 11.45 11.45C10.9278 11.9722 10.6667 12.6 10.6667 13.3333C10.6667 14.0667 10.9278 14.6944 11.45 15.2167C11.9722 15.7389 12.6 16 13.3333 16Z',
    ]}
    width={width}
    height={height}
    fill={fill}
    viewBox={viewBox}
    {...props}
  />
)

export const MenuList = ({
  width = iconSize.large,
  height = iconSize.large,
  fill,
  viewBox = '0 0 32 32',
  ...props
}: IconProps) => (
  <BaseIcon
    paths={[
      'M10.6685 26.6673C9.93516 26.6673 9.30738 26.4062 8.78516 25.884C8.26293 25.3618 8.00182 24.734 8.00182 24.0007V21.334C8.00182 20.9562 8.1296 20.6395 8.38516 20.384C8.64071 20.1284 8.95738 20.0007 9.33516 20.0007H12.0018V17.0007C11.3352 16.9562 10.674 16.8173 10.0185 16.584C9.36293 16.3507 8.79071 16.0007 8.30182 15.534C8.16849 15.4007 8.06293 15.2507 7.98516 15.084C7.90738 14.9173 7.86849 14.7451 7.86849 14.5673V13.6673H6.90182C6.72405 13.6673 6.55182 13.634 6.38516 13.5673C6.21849 13.5007 6.06849 13.4007 5.93516 13.2673L2.93516 10.2673C2.66849 10.0007 2.53516 9.68954 2.53516 9.33398C2.53516 8.97843 2.66849 8.66732 2.93516 8.40065C3.5796 7.75621 4.44627 7.28398 5.53516 6.98398C6.62405 6.68398 7.62405 6.53398 8.53516 6.53398C9.13516 6.53398 9.71849 6.57843 10.2852 6.66732C10.8518 6.75621 11.424 6.92287 12.0018 7.16732C12.0018 6.65621 12.1796 6.22287 12.5352 5.86732C12.8907 5.51176 13.324 5.33398 13.8352 5.33398H25.3352C26.0685 5.33398 26.6963 5.5951 27.2185 6.11732C27.7407 6.63954 28.0018 7.26732 28.0018 8.00065V22.6673C28.0018 23.7784 27.6129 24.7229 26.8352 25.5007C26.0574 26.2784 25.1129 26.6673 24.0018 26.6673H10.6685ZM14.6685 20.0007H21.3352C21.7129 20.0007 22.0296 20.1284 22.2852 20.384C22.5407 20.6395 22.6685 20.9562 22.6685 21.334V22.6673C22.6685 23.0451 22.7963 23.3618 23.0518 23.6173C23.3074 23.8729 23.624 24.0007 24.0018 24.0007C24.3796 24.0007 24.6963 23.8729 24.9518 23.6173C25.2074 23.3618 25.3352 23.0451 25.3352 22.6673V8.00065H14.6685V8.80065L22.3018 16.434C22.5018 16.634 22.624 16.8618 22.6685 17.1173C22.7129 17.3729 22.6796 17.6229 22.5685 17.8673C22.4574 18.1118 22.3018 18.3062 22.1018 18.4507C21.9018 18.5951 21.6463 18.6673 21.3352 18.6673C21.1574 18.6673 20.9852 18.6284 20.8185 18.5507C20.6518 18.4729 20.5129 18.3784 20.4018 18.2673L17.0018 14.8673L16.7352 15.134C16.424 15.4451 16.0963 15.7229 15.7518 15.9673C15.4074 16.2118 15.0463 16.4007 14.6685 16.534V20.0007ZM7.46849 11.0007H9.20182C9.5796 11.0007 9.89627 11.1284 10.1518 11.384C10.4074 11.6395 10.5352 11.9562 10.5352 12.334V13.8673C10.8018 14.0451 11.0796 14.1673 11.3685 14.234C11.6574 14.3007 11.9574 14.334 12.2685 14.334C12.7796 14.334 13.2407 14.2562 13.6518 14.1007C14.0629 13.9451 14.4685 13.6673 14.8685 13.2673L15.1352 13.0007L13.2685 11.134C12.624 10.4895 11.9018 10.0062 11.1018 9.68398C10.3018 9.36176 9.44627 9.20065 8.53516 9.20065C8.09071 9.20065 7.66849 9.23398 7.26849 9.30065C6.86849 9.36732 6.46849 9.46732 6.06849 9.60065L7.46849 11.0007ZM20.0018 22.6673H10.6685V24.0007H20.2018C20.1352 23.8006 20.0852 23.5895 20.0518 23.3673C20.0185 23.1451 20.0018 22.9118 20.0018 22.6673Z',
    ]}
    width={width}
    height={height}
    fill={fill}
    viewBox={viewBox}
    {...props}
  />
)

export const Download = ({
  width = iconSize.large,
  height = iconSize.large,
  fill,
  viewBox = '0 0 33 32',
  ...props
}: IconProps) => (
  <BaseIcon
    paths={[
      'M16.6628 20.7673C16.485 20.7673 16.3183 20.7395 16.1628 20.684C16.0072 20.6284 15.8628 20.534 15.7294 20.4007L10.9294 15.6007C10.6628 15.334 10.535 15.0229 10.5461 14.6673C10.5572 14.3118 10.685 14.0007 10.9294 13.734C11.1961 13.4673 11.5128 13.3284 11.8794 13.3173C12.2461 13.3062 12.5628 13.434 12.8294 13.7007L15.3294 16.2007V6.66732C15.3294 6.28954 15.4572 5.97287 15.7128 5.71732C15.9683 5.46176 16.285 5.33398 16.6628 5.33398C17.0405 5.33398 17.3572 5.46176 17.6128 5.71732C17.8683 5.97287 17.9961 6.28954 17.9961 6.66732V16.2007L20.4961 13.7007C20.7628 13.434 21.0794 13.3062 21.4461 13.3173C21.8128 13.3284 22.1294 13.4673 22.3961 13.734C22.6405 14.0007 22.7683 14.3118 22.7794 14.6673C22.7905 15.0229 22.6628 15.334 22.3961 15.6007L17.5961 20.4007C17.4628 20.534 17.3183 20.6284 17.1628 20.684C17.0072 20.7395 16.8405 20.7673 16.6628 20.7673ZM8.66276 26.6673C7.92943 26.6673 7.30165 26.4062 6.77943 25.884C6.2572 25.3618 5.99609 24.734 5.99609 24.0007V21.334C5.99609 20.9562 6.12387 20.6395 6.37943 20.384C6.63498 20.1284 6.95165 20.0007 7.32943 20.0007C7.7072 20.0007 8.02387 20.1284 8.27943 20.384C8.53498 20.6395 8.66276 20.9562 8.66276 21.334V24.0007H24.6628V21.334C24.6628 20.9562 24.7905 20.6395 25.0461 20.384C25.3016 20.1284 25.6183 20.0007 25.9961 20.0007C26.3739 20.0007 26.6905 20.1284 26.9461 20.384C27.2016 20.6395 27.3294 20.9562 27.3294 21.334V24.0007C27.3294 24.734 27.0683 25.3618 26.5461 25.884C26.0239 26.4062 25.3961 26.6673 24.6628 26.6673H8.66276Z',
    ]}
    width={width}
    height={height}
    fill={fill}
    viewBox={viewBox}
    {...props}
  />
)

export const ExpandCircleRight = ({
  width = iconSize.medium,
  height = iconSize.medium,
  fill,
  viewBox = '0 0 24 24',
  ...props
}: IconProps) => (
  <BaseIcon
    paths={[
      'M12.675 12L10.3 14.375C10.1167 14.5583 10.025 14.7875 10.025 15.0625C10.025 15.3375 10.1167 15.575 10.3 15.775C10.5 15.975 10.7375 16.075 11.0125 16.075C11.2875 16.075 11.525 15.975 11.725 15.775L14.8 12.7C15 12.5 15.1 12.2667 15.1 12C15.1 11.7333 15 11.5 14.8 11.3L11.7 8.2C11.5 8 11.2667 7.90417 11 7.9125C10.7333 7.92083 10.5 8.025 10.3 8.225C10.1167 8.425 10.0208 8.65833 10.0125 8.925C10.0042 9.19167 10.1 9.425 10.3 9.625L12.675 12ZM12 22C10.6167 22 9.31667 21.7375 8.1 21.2125C6.88333 20.6875 5.825 19.975 4.925 19.075C4.025 18.175 3.3125 17.1167 2.7875 15.9C2.2625 14.6833 2 13.3833 2 12C2 10.6167 2.2625 9.31667 2.7875 8.1C3.3125 6.88333 4.025 5.825 4.925 4.925C5.825 4.025 6.88333 3.3125 8.1 2.7875C9.31667 2.2625 10.6167 2 12 2C13.3833 2 14.6833 2.2625 15.9 2.7875C17.1167 3.3125 18.175 4.025 19.075 4.925C19.975 5.825 20.6875 6.88333 21.2125 8.1C21.7375 9.31667 22 10.6167 22 12C22 13.3833 21.7375 14.6833 21.2125 15.9C20.6875 17.1167 19.975 18.175 19.075 19.075C18.175 19.975 17.1167 20.6875 15.9 21.2125C14.6833 21.7375 13.3833 22 12 22ZM12 20C14.2333 20 16.125 19.225 17.675 17.675C19.225 16.125 20 14.2333 20 12C20 9.76667 19.225 7.875 17.675 6.325C16.125 4.775 14.2333 4 12 4C9.76667 4 7.875 4.775 6.325 6.325C4.775 7.875 4 9.76667 4 12C4 14.2333 4.775 16.125 6.325 17.675C7.875 19.225 9.76667 20 12 20Z',
    ]}
    width={width}
    height={height}
    fill={fill}
    viewBox={viewBox}
    {...props}
  />
)

export const SupportAgent = ({
  width = iconSize.large,
  height = iconSize.large,
  fill,
  viewBox = '0 0 32 32',
  ...props
}: IconProps) => (
  <BaseIcon
    paths={[
      'M14.668 28V25.3333H25.3346V15.8667C25.3346 13.2667 24.4291 11.0611 22.618 9.25C20.8069 7.43889 18.6013 6.53333 16.0013 6.53333C13.4013 6.53333 11.1957 7.43889 9.38464 9.25C7.57352 11.0611 6.66797 13.2667 6.66797 15.8667V24H5.33464C4.6013 24 3.97352 23.7389 3.4513 23.2167C2.92908 22.6944 2.66797 22.0667 2.66797 21.3333V18.6667C2.66797 18.2 2.78464 17.7611 3.01797 17.35C3.2513 16.9389 3.57908 16.6111 4.0013 16.3667L4.1013 14.6C4.27908 13.0889 4.71797 11.6889 5.41797 10.4C6.11797 9.11111 6.99575 7.98889 8.0513 7.03333C9.10686 6.07778 10.318 5.33333 11.6846 4.8C13.0513 4.26667 14.4902 4 16.0013 4C17.5124 4 18.9457 4.26667 20.3013 4.8C21.6569 5.33333 22.868 6.07222 23.9346 7.01667C25.0013 7.96111 25.8791 9.07778 26.568 10.3667C27.2569 11.6556 27.7013 13.0556 27.9013 14.5667L28.0013 16.3C28.4235 16.5 28.7513 16.8 28.9846 17.2C29.218 17.6 29.3346 18.0222 29.3346 18.4667V21.5333C29.3346 21.9778 29.218 22.4 28.9846 22.8C28.7513 23.2 28.4235 23.5 28.0013 23.7V25.3333C28.0013 26.0667 27.7402 26.6944 27.218 27.2167C26.6957 27.7389 26.068 28 25.3346 28H14.668ZM12.0013 18.6667C11.6235 18.6667 11.3069 18.5389 11.0513 18.2833C10.7957 18.0278 10.668 17.7111 10.668 17.3333C10.668 16.9556 10.7957 16.6389 11.0513 16.3833C11.3069 16.1278 11.6235 16 12.0013 16C12.3791 16 12.6957 16.1278 12.9513 16.3833C13.2069 16.6389 13.3346 16.9556 13.3346 17.3333C13.3346 17.7111 13.2069 18.0278 12.9513 18.2833C12.6957 18.5389 12.3791 18.6667 12.0013 18.6667ZM20.0013 18.6667C19.6235 18.6667 19.3069 18.5389 19.0513 18.2833C18.7957 18.0278 18.668 17.7111 18.668 17.3333C18.668 16.9556 18.7957 16.6389 19.0513 16.3833C19.3069 16.1278 19.6235 16 20.0013 16C20.3791 16 20.6957 16.1278 20.9513 16.3833C21.2069 16.6389 21.3346 16.9556 21.3346 17.3333C21.3346 17.7111 21.2069 18.0278 20.9513 18.2833C20.6957 18.5389 20.3791 18.6667 20.0013 18.6667ZM8.03464 16.6C7.87908 14.2444 8.59019 12.2222 10.168 10.5333C11.7457 8.84444 13.7124 8 16.068 8C18.0457 8 19.7846 8.62778 21.2846 9.88333C22.7846 11.1389 23.6902 12.7444 24.0013 14.7C21.9791 14.6778 20.118 14.1333 18.418 13.0667C16.718 12 15.4124 10.5556 14.5013 8.73333C14.1457 10.5111 13.3957 12.0944 12.2513 13.4833C11.1069 14.8722 9.7013 15.9111 8.03464 16.6Z',
    ]}
    width={width}
    height={height}
    fill={fill}
    viewBox={viewBox}
    {...props}
  />
)

export const KeyboardArrowLeft = ({
  width = iconSize.extraSmall,
  height = iconSize.extraSmall,
  fill = 'white',
  viewBox = '0 0 18 18',
  ...props
}: IconProps) => (
  <BaseIcon
    paths={[
      'M8.09961 8.99961L11.0246 11.9246C11.1621 12.0621 11.2309 12.2371 11.2309 12.4496C11.2309 12.6621 11.1621 12.8371 11.0246 12.9746C10.8871 13.1121 10.7121 13.1809 10.4996 13.1809C10.2871 13.1809 10.1121 13.1121 9.97461 12.9746L6.52461 9.52461C6.44961 9.44961 6.39648 9.36836 6.36523 9.28086C6.33398 9.19336 6.31836 9.09961 6.31836 8.99961C6.31836 8.89961 6.33398 8.80586 6.36523 8.71836C6.39648 8.63086 6.44961 8.54961 6.52461 8.47461L9.97461 5.02461C10.1121 4.88711 10.2871 4.81836 10.4996 4.81836C10.7121 4.81836 10.8871 4.88711 11.0246 5.02461C11.1621 5.16211 11.2309 5.33711 11.2309 5.54961C11.2309 5.76211 11.1621 5.93711 11.0246 6.07461L8.09961 8.99961Z',
    ]}
    width={width}
    height={height}
    fill={fill}
    viewBox={viewBox}
    {...props}
  />
)

export const KeyboardArrowRight = ({
  width = iconSize.extraSmall,
  height = iconSize.extraSmall,
  fill = 'white',
  viewBox = '0 0 18 18',
  ...props
}: IconProps) => (
  <BaseIcon
    paths={[
      'M9.44961 8.99961L6.52461 6.07461C6.38711 5.93711 6.31836 5.76211 6.31836 5.54961C6.31836 5.33711 6.38711 5.16211 6.52461 5.02461C6.66211 4.88711 6.83711 4.81836 7.04961 4.81836C7.26211 4.81836 7.43711 4.88711 7.57461 5.02461L11.0246 8.47461C11.0996 8.54961 11.1527 8.63086 11.184 8.71836C11.2152 8.80586 11.2309 8.89961 11.2309 8.99961C11.2309 9.09961 11.2152 9.19336 11.184 9.28086C11.1527 9.36836 11.0996 9.44961 11.0246 9.52461L7.57461 12.9746C7.43711 13.1121 7.26211 13.1809 7.04961 13.1809C6.83711 13.1809 6.66211 13.1121 6.52461 12.9746C6.38711 12.8371 6.31836 12.6621 6.31836 12.4496C6.31836 12.2371 6.38711 12.0621 6.52461 11.9246L9.44961 8.99961Z',
    ]}
    width={width}
    height={height}
    fill={fill}
    viewBox={viewBox}
    {...props}
  />
)

export const KeyboardArrowUp = ({
  width = iconSize.extraSmall,
  height = iconSize.extraSmall,
  fill = '#B3B3B3',
  viewBox = '0 0 24 24',
  ...props
}: IconProps) => (
  <BaseIcon
    paths={[
      'M12.0008 10.8004L8.10078 14.7004C7.91745 14.8837 7.68411 14.9754 7.40078 14.9754C7.11745 14.9754 6.88411 14.8837 6.70078 14.7004C6.51745 14.5171 6.42578 14.2837 6.42578 14.0004C6.42578 13.7171 6.51745 13.4837 6.70078 13.3004L11.3008 8.70039C11.5008 8.50039 11.7341 8.40039 12.0008 8.40039C12.2674 8.40039 12.5008 8.50039 12.7008 8.70039L17.3008 13.3004C17.4841 13.4837 17.5758 13.7171 17.5758 14.0004C17.5758 14.2837 17.4841 14.5171 17.3008 14.7004C17.1174 14.8837 16.8841 14.9754 16.6008 14.9754C16.3174 14.9754 16.0841 14.8837 15.9008 14.7004L12.0008 10.8004Z',
    ]}
    width={width}
    height={height}
    fill={fill}
    viewBox={viewBox}
    {...props}
  />
)

export const KeyboardArrowDown = ({
  width = iconSize.extraSmall,
  height = iconSize.extraSmall,
  fill = 'white',
  viewBox = '0 0 24 24',
  ...props
}: IconProps) => (
  <BaseIcon
    paths={[
      'M12.0008 14.9758C11.8674 14.9758 11.7424 14.9549 11.6258 14.9133C11.5091 14.8716 11.4008 14.8008 11.3008 14.7008L6.70078 10.1008C6.51745 9.91745 6.42578 9.68411 6.42578 9.40078C6.42578 9.11745 6.51745 8.88411 6.70078 8.70078C6.88411 8.51745 7.11745 8.42578 7.40078 8.42578C7.68411 8.42578 7.91745 8.51745 8.10078 8.70078L12.0008 12.6008L15.9008 8.70078C16.0841 8.51745 16.3174 8.42578 16.6008 8.42578C16.8841 8.42578 17.1174 8.51745 17.3008 8.70078C17.4841 8.88411 17.5758 9.11745 17.5758 9.40078C17.5758 9.68411 17.4841 9.91745 17.3008 10.1008L12.7008 14.7008C12.6008 14.8008 12.4924 14.8716 12.3758 14.9133C12.2591 14.9549 12.1341 14.9758 12.0008 14.9758Z',
    ]}
    width={width}
    height={height}
    fill={fill}
    viewBox={viewBox}
    {...props}
  />
)

export const EventNoteIcon = ({
  width = iconSize.extraSmall,
  height = iconSize.extraSmall,
  fill = 'white',
  viewBox = '0 0 24 24',
  ...props
}: IconProps) => (
  <BaseIcon
    paths={[
      'M5 22C4.45 22 3.97917 21.8042 3.5875 21.4125C3.19583 21.0208 3 20.55 3 20V6C3 5.45 3.19583 4.97917 3.5875 4.5875C3.97917 4.19583 4.45 4 5 4H6V3C6 2.71667 6.09583 2.47917 6.2875 2.2875C6.47917 2.09583 6.71667 2 7 2C7.28333 2 7.52083 2.09583 7.7125 2.2875C7.90417 2.47917 8 2.71667 8 3V4H16V3C16 2.71667 16.0958 2.47917 16.2875 2.2875C16.4792 2.09583 16.7167 2 17 2C17.2833 2 17.5208 2.09583 17.7125 2.2875C17.9042 2.47917 18 2.71667 18 3V4H19C19.55 4 20.0208 4.19583 20.4125 4.5875C20.8042 4.97917 21 5.45 21 6V20C21 20.55 20.8042 21.0208 20.4125 21.4125C20.0208 21.8042 19.55 22 19 22H5ZM5 20H19V10H5V20ZM8 14C7.71667 14 7.47917 13.9042 7.2875 13.7125C7.09583 13.5208 7 13.2833 7 13C7 12.7167 7.09583 12.4792 7.2875 12.2875C7.47917 12.0958 7.71667 12 8 12H16C16.2833 12 16.5208 12.0958 16.7125 12.2875C16.9042 12.4792 17 12.7167 17 13C17 13.2833 16.9042 13.5208 16.7125 13.7125C16.5208 13.9042 16.2833 14 16 14H8ZM8 18C7.71667 18 7.47917 17.9042 7.2875 17.7125C7.09583 17.5208 7 17.2833 7 17C7 16.7167 7.09583 16.4792 7.2875 16.2875C7.47917 16.0958 7.71667 16 8 16H13C13.2833 16 13.5208 16.0958 13.7125 16.2875C13.9042 16.4792 14 16.7167 14 17C14 17.2833 13.9042 17.5208 13.7125 17.7125C13.5208 17.9042 13.2833 18 13 18H8Z',
    ]}
    width={width}
    height={height}
    fill={fill}
    viewBox={viewBox}
    {...props}
  />
)

export const DeveloperGuideIcon = ({
  width = iconSize.extraSmall,
  height = iconSize.extraSmall,
  fill = 'white',
  viewBox = '0 0 24 24',
  ...props
}: IconProps) => (
  <BaseIcon
    paths={[
      'M5 21C4.45 21 3.97917 20.8042 3.5875 20.4125C3.19583 20.0208 3 19.55 3 19V5C3 4.45 3.19583 3.97917 3.5875 3.5875C3.97917 3.19583 4.45 3 5 3H19C19.55 3 20.0208 3.19583 20.4125 3.5875C20.8042 3.97917 21 4.45 21 5V19C21 19.55 20.8042 20.0208 20.4125 20.4125C20.0208 20.8042 19.55 21 19 21H5ZM5 5V19H19V5H17V11.125C17 11.325 16.9167 11.4708 16.75 11.5625C16.5833 11.6542 16.4167 11.65 16.25 11.55L15.025 10.8C14.8583 10.7 14.6833 10.65 14.5 10.65C14.3167 10.65 14.1417 10.7 13.975 10.8L12.75 11.55C12.5833 11.65 12.4167 11.6542 12.25 11.5625C12.0833 11.4708 12 11.325 12 11.125V5H5Z',
    ]}
    width={width}
    height={height}
    fill={fill}
    viewBox={viewBox}
    {...props}
  />
)

export const CheckCircleIcon = ({
  width = iconSize.large,
  height = iconSize.large,
  fill,
  viewBox = '0 0 32 32',
  ...props
}: IconProps) => (
  <BaseIcon
    paths={[
      'M14.1346 18.3994L11.268 15.5327C11.0235 15.2882 10.7124 15.166 10.3346 15.166C9.95686 15.166 9.64575 15.2882 9.4013 15.5327C9.15686 15.7771 9.03464 16.0882 9.03464 16.466C9.03464 16.8438 9.15686 17.1549 9.4013 17.3994L13.2013 21.1994C13.468 21.466 13.7791 21.5993 14.1346 21.5993C14.4902 21.5993 14.8013 21.466 15.068 21.1994L22.6013 13.666C22.8457 13.4216 22.968 13.1105 22.968 12.7327C22.968 12.3549 22.8457 12.0438 22.6013 11.7993C22.3569 11.5549 22.0457 11.4327 21.668 11.4327C21.2902 11.4327 20.9791 11.5549 20.7346 11.7993L14.1346 18.3994ZM16.0013 29.3327C14.1569 29.3327 12.4235 28.9827 10.8013 28.2827C9.17908 27.5827 7.76797 26.6327 6.56797 25.4327C5.36797 24.2327 4.41797 22.8216 3.71797 21.1994C3.01797 19.5771 2.66797 17.8438 2.66797 15.9993C2.66797 14.1549 3.01797 12.4216 3.71797 10.7993C4.41797 9.17713 5.36797 7.76602 6.56797 6.56602C7.76797 5.36602 9.17908 4.41602 10.8013 3.71602C12.4235 3.01602 14.1569 2.66602 16.0013 2.66602C17.8457 2.66602 19.5791 3.01602 21.2013 3.71602C22.8235 4.41602 24.2346 5.36602 25.4346 6.56602C26.6346 7.76602 27.5846 9.17713 28.2846 10.7993C28.9846 12.4216 29.3346 14.1549 29.3346 15.9993C29.3346 17.8438 28.9846 19.5771 28.2846 21.1994C27.5846 22.8216 26.6346 24.2327 25.4346 25.4327C24.2346 26.6327 22.8235 27.5827 21.2013 28.2827C19.5791 28.9827 17.8457 29.3327 16.0013 29.3327ZM16.0013 26.666C18.9791 26.666 21.5013 25.6327 23.568 23.566C25.6346 21.4993 26.668 18.9771 26.668 15.9993C26.668 13.0216 25.6346 10.4993 23.568 8.43268C21.5013 6.36602 18.9791 5.33268 16.0013 5.33268C13.0235 5.33268 10.5013 6.36602 8.43464 8.43268C6.36797 10.4993 5.33464 13.0216 5.33464 15.9993C5.33464 18.9771 6.36797 21.4993 8.43464 23.566C10.5013 25.6327 13.0235 26.666 16.0013 26.666Z',
    ]}
    width={width}
    height={height}
    fill={fill}
    viewBox={viewBox}
    {...props}
  />
)

export const TrendingUpIcon = ({
  width = iconSize.medium,
  height = iconSize.medium,
  fill,
  viewBox = '0 0 24 24',
  ...props
}: IconProps) => (
  <BaseIcon
    paths={[
      'M2.59813 17.7514C2.3703 17.5275 2.25955 17.2592 2.26588 16.9464C2.27222 16.6334 2.38105 16.3693 2.59238 16.1541L7.81338 10.8759C8.25255 10.4367 8.79138 10.2171 9.42988 10.2171C10.0682 10.2171 10.607 10.4367 11.0461 10.8759L13.4299 13.2786L18.3309 8.41563H16.9341C16.615 8.41563 16.3455 8.30579 16.1259 8.08612C15.9064 7.86662 15.7966 7.59729 15.7966 7.27813C15.7966 6.95879 15.9064 6.68937 16.1259 6.46987C16.3455 6.25037 16.615 6.14062 16.9341 6.14062H21.0656C21.3848 6.14062 21.6542 6.25037 21.8739 6.46987C22.0934 6.68937 22.2031 6.95879 22.2031 7.27813V11.4036C22.2031 11.7228 22.0934 11.9921 21.8739 12.2116C21.6542 12.4313 21.3848 12.5411 21.0656 12.5411C20.7465 12.5411 20.477 12.4313 20.2574 12.2116C20.0379 11.9921 19.9281 11.7228 19.9281 11.4036V10.0069L15.0461 14.8889C14.607 15.328 14.0682 15.5476 13.4299 15.5476C12.7914 15.5476 12.2525 15.328 11.8134 14.8889L9.42988 12.5111L4.18963 17.7514C3.9783 17.9627 3.71305 18.0684 3.39388 18.0684C3.07472 18.0684 2.80947 17.9627 2.59813 17.7514Z',
    ]}
    width={width}
    height={height}
    fill={fill}
    viewBox={viewBox}
    {...props}
  />
)

export const TrendingDownIcon = ({
  width = iconSize.medium,
  height = iconSize.medium,
  fill,
  viewBox = '0 0 24 24',
  ...props
}: IconProps) => (
  <BaseIcon
    paths={[
      'M18.3309 15.9045L13.4299 11.0415L11.0461 13.4252C10.607 13.8644 10.0682 14.084 9.42988 14.084C8.79138 14.084 8.25255 13.8644 7.81338 13.4252L2.59238 8.15995C2.38105 7.94479 2.27222 7.68179 2.26588 7.37095C2.25955 7.05995 2.3703 6.79254 2.59813 6.5687C2.80947 6.35754 3.07472 6.25195 3.39388 6.25195C3.71305 6.25195 3.9783 6.35754 4.18963 6.5687L9.42988 11.809L11.8134 9.4252C12.2525 8.98604 12.7914 8.76645 13.4299 8.76645C14.0682 8.76645 14.607 8.98604 15.0461 9.4252L19.9281 14.3132V12.9165C19.9281 12.5973 20.0379 12.328 20.2574 12.1085C20.477 11.8888 20.7465 11.779 21.0656 11.779C21.3848 11.779 21.6542 11.8888 21.8739 12.1085C22.0934 12.328 22.2031 12.5973 22.2031 12.9165V17.042C22.2031 17.3613 22.0934 17.6307 21.8739 17.8502C21.6542 18.0697 21.3848 18.1795 21.0656 18.1795H16.9341C16.615 18.1795 16.3455 18.0697 16.1259 17.8502C15.9064 17.6307 15.7966 17.3613 15.7966 17.042C15.7966 16.7228 15.9064 16.4535 16.1259 16.234C16.3455 16.0143 16.615 15.9045 16.9341 15.9045H18.3309Z',
    ]}
    width={width}
    height={height}
    fill={fill}
    viewBox={viewBox}
    {...props}
  />
)

export const CloseIcon = ({
  width = iconSize.small,
  height = iconSize.small,
  fill,
  viewBox = '0 0 16 16',
  ...props
}: IconProps) => (
  <BaseIcon
    paths={[
      'M12.6562 4.28125L8.9375 8L12.6562 11.7188L11.7188 12.6562L8 8.9375L4.28125 12.6562L3.34375 11.7188L7.0625 8L3.34375 4.28125L4.28125 3.34375L8 7.0625L11.7188 3.34375L12.6562 4.28125Z',
    ]}
    width={width}
    height={height}
    fill={fill}
    viewBox={viewBox}
    {...props}
  />
)

export const CancelIcon = ({
  width = iconSize.large,
  height = iconSize.large,
  fill,
  viewBox = '0 0 32 32',
  ...props
}: IconProps) => (
  <BaseIcon
    paths={[
      'M16.0013 17.867L19.868 21.7337C20.1124 21.9781 20.4235 22.1003 20.8013 22.1003C21.1791 22.1003 21.4902 21.9781 21.7346 21.7337C21.9791 21.4892 22.1013 21.1781 22.1013 20.8003C22.1013 20.4225 21.9791 20.1114 21.7346 19.867L17.868 16.0003L21.7346 12.1337C21.9791 11.8892 22.1013 11.5781 22.1013 11.2003C22.1013 10.8225 21.9791 10.5114 21.7346 10.267C21.4902 10.0225 21.1791 9.90033 20.8013 9.90033C20.4235 9.90033 20.1124 10.0225 19.868 10.267L16.0013 14.1337L12.1346 10.267C11.8902 10.0225 11.5791 9.90033 11.2013 9.90033C10.8235 9.90033 10.5124 10.0225 10.268 10.267C10.0235 10.5114 9.9013 10.8225 9.9013 11.2003C9.9013 11.5781 10.0235 11.8892 10.268 12.1337L14.1346 16.0003L10.268 19.867C10.0235 20.1114 9.9013 20.4225 9.9013 20.8003C9.9013 21.1781 10.0235 21.4892 10.268 21.7337C10.5124 21.9781 10.8235 22.1003 11.2013 22.1003C11.5791 22.1003 11.8902 21.9781 12.1346 21.7337L16.0013 17.867ZM16.0013 29.3337C14.1569 29.3337 12.4235 28.9837 10.8013 28.2837C9.17908 27.5837 7.76797 26.6337 6.56797 25.4337C5.36797 24.2337 4.41797 22.8225 3.71797 21.2003C3.01797 19.5781 2.66797 17.8448 2.66797 16.0003C2.66797 14.1559 3.01797 12.4225 3.71797 10.8003C4.41797 9.1781 5.36797 7.76699 6.56797 6.56699C7.76797 5.36699 9.17908 4.41699 10.8013 3.71699C12.4235 3.01699 14.1569 2.66699 16.0013 2.66699C17.8457 2.66699 19.5791 3.01699 21.2013 3.71699C22.8235 4.41699 24.2346 5.36699 25.4346 6.56699C26.6346 7.76699 27.5846 9.1781 28.2846 10.8003C28.9846 12.4225 29.3346 14.1559 29.3346 16.0003C29.3346 17.8448 28.9846 19.5781 28.2846 21.2003C27.5846 22.8225 26.6346 24.2337 25.4346 25.4337C24.2346 26.6337 22.8235 27.5837 21.2013 28.2837C19.5791 28.9837 17.8457 29.3337 16.0013 29.3337ZM16.0013 26.667C18.9791 26.667 21.5013 25.6337 23.568 23.567C25.6346 21.5003 26.668 18.9781 26.668 16.0003C26.668 13.0225 25.6346 10.5003 23.568 8.43366C21.5013 6.36699 18.9791 5.33366 16.0013 5.33366C13.0235 5.33366 10.5013 6.36699 8.43464 8.43366C6.36797 10.5003 5.33464 13.0225 5.33464 16.0003C5.33464 18.9781 6.36797 21.5003 8.43464 23.567C10.5013 25.6337 13.0235 26.667 16.0013 26.667Z',
    ]}
    width={width}
    height={height}
    fill={fill}
    viewBox={viewBox}
    {...props}
  />
)

export const InfoCircleIcon = ({
  width = iconSize.smallMedium,
  height = iconSize.smallMedium,
  fill,
  viewBox = '0 0 20 20',
  ...props
}: IconProps) => (
  <BaseIcon
    paths={[
      'M9.95964 15.0003C10.2513 15.0003 10.4978 14.8996 10.6992 14.6982C10.9006 14.4969 11.0013 14.2503 11.0013 13.9587C11.0013 13.667 10.9006 13.4205 10.6992 13.2191C10.4978 13.0177 10.2513 12.917 9.95964 12.917C9.66797 12.917 9.42144 13.0177 9.22005 13.2191C9.01866 13.4205 8.91797 13.667 8.91797 13.9587C8.91797 14.2503 9.01866 14.4969 9.22005 14.6982C9.42144 14.8996 9.66797 15.0003 9.95964 15.0003ZM10.0013 18.3337C8.84853 18.3337 7.76519 18.1149 6.7513 17.6774C5.73741 17.2399 4.85547 16.6462 4.10547 15.8962C3.35547 15.1462 2.76172 14.2642 2.32422 13.2503C1.88672 12.2364 1.66797 11.1531 1.66797 10.0003C1.66797 8.84755 1.88672 7.76421 2.32422 6.75033C2.76172 5.73644 3.35547 4.85449 4.10547 4.10449C4.85547 3.35449 5.73741 2.76074 6.7513 2.32324C7.76519 1.88574 8.84853 1.66699 10.0013 1.66699C11.1541 1.66699 12.2374 1.88574 13.2513 2.32324C14.2652 2.76074 15.1471 3.35449 15.8971 4.10449C16.6471 4.85449 17.2409 5.73644 17.6784 6.75033C18.1159 7.76421 18.3346 8.84755 18.3346 10.0003C18.3346 11.1531 18.1159 12.2364 17.6784 13.2503C17.2409 14.2642 16.6471 15.1462 15.8971 15.8962C15.1471 16.6462 14.2652 17.2399 13.2513 17.6774C12.2374 18.1149 11.1541 18.3337 10.0013 18.3337ZM10.0013 16.667C11.8624 16.667 13.4388 16.0212 14.7305 14.7295C16.0221 13.4378 16.668 11.8614 16.668 10.0003C16.668 8.13921 16.0221 6.56283 14.7305 5.27116C13.4388 3.97949 11.8624 3.33366 10.0013 3.33366C8.14019 3.33366 6.5638 3.97949 5.27214 5.27116C3.98047 6.56283 3.33464 8.13921 3.33464 10.0003C3.33464 11.8614 3.98047 13.4378 5.27214 14.7295C6.5638 16.0212 8.14019 16.667 10.0013 16.667ZM10.0846 6.41699C10.4319 6.41699 10.7339 6.5281 10.9909 6.75033C11.2478 6.97255 11.3763 7.25033 11.3763 7.58366C11.3763 7.88921 11.2826 8.16005 11.0951 8.39616C10.9076 8.63227 10.6957 8.85449 10.4596 9.06283C10.1402 9.3406 9.85894 9.64616 9.61589 9.97949C9.37283 10.3128 9.2513 10.6878 9.2513 11.1045C9.2513 11.2989 9.32422 11.4621 9.47005 11.5941C9.61589 11.726 9.78603 11.792 9.98047 11.792C10.1888 11.792 10.3659 11.7225 10.5117 11.5837C10.6576 11.4448 10.7513 11.2712 10.793 11.0628C10.8485 10.7712 10.9735 10.5107 11.168 10.2816C11.3624 10.0524 11.5707 9.83366 11.793 9.62533C12.1124 9.31977 12.3867 8.98644 12.6159 8.62533C12.8451 8.26421 12.9596 7.86144 12.9596 7.41699C12.9596 6.70866 12.6714 6.1288 12.0951 5.67741C11.5187 5.22602 10.8485 5.00033 10.0846 5.00033C9.55686 5.00033 9.05339 5.11144 8.57422 5.33366C8.09505 5.55588 7.73047 5.89616 7.48047 6.35449C7.38325 6.52116 7.352 6.69824 7.38672 6.88574C7.42144 7.07324 7.51519 7.2156 7.66797 7.31283C7.86241 7.42394 8.0638 7.45866 8.27214 7.41699C8.48047 7.37533 8.65408 7.25727 8.79297 7.06283C8.94575 6.85449 9.13672 6.69477 9.36589 6.58366C9.59505 6.47255 9.83464 6.41699 10.0846 6.41699Z',
    ]}
    width={width}
    height={height}
    fill={fill}
    viewBox={viewBox}
    {...props}
  />
)
