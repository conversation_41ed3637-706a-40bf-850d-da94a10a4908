import clsx from 'clsx'

type ButtonProps = {
  id: string
  label: string
  type: string
  className?: string
  required?: boolean
  onClick?: () => void
  error?: string
}

const Input = ({
  type,
  id,
  required = true,
  error,
  label,
  className,
  ...rest
}: ButtonProps) => {
  return (
    <div>
      <label
        htmlFor={id}
        className={
          'mb-2 block text-sm font-medium text-gray-900 dark:text-white'
        }
      >
        {label}
      </label>
      <input
        type={type}
        id={id}
        className={clsx(
          'block w-full rounded-lg border border-gray-300 bg-gray-50  p-2.5 text-sm text-gray-900 ',
          className,
          {
            'border-red-600 dark:border-red-500': error,
            'focus:ring-blue-500 focus:border-blue-500': !error,
          },
        )}
        required={required}
        {...rest}
      />
      {error && (
        <p className={'mt-2 text-sm text-red-600 dark:text-red-500'}>{error}</p>
      )}
    </div>
  )
}

export default Input
