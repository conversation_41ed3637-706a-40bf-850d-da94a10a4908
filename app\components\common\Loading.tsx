import { useEffect, useState } from 'react'
import loading1 from '../../assets/loading/loading1.png'
import loading2 from '../../assets/loading/loading2.png'
import loading3 from '../../assets/loading/loading3.png'
import loading4 from '../../assets/loading/loading4.png'
import loading5 from '../../assets/loading/loading5.png'
import loading6 from '../../assets/loading/loading6.png'
import loading7 from '../../assets/loading/loading7.png'
import loading8 from '../../assets/loading/loading8.png'
import loading9 from '../../assets/loading/loading9.png'

const images = [
  loading1,
  loading2,
  loading3,
  loading4,
  loading5,
  loading6,
  loading7,
  loading8,
  loading9,
]

const Loading = () => {
  const [currentIndex, setCurrentIndex] = useState(0)

  useEffect(() => {
    images.forEach((src) => {
      const img = new Image()
      img.src = src
    })
  }, [])

  useEffect(() => {
    let interval: number

    const updateIndex = () => {
      setCurrentIndex((prevIndex) => {
        const newIndex = (prevIndex + 1) % images.length
        return newIndex
      })
    }

    const updateInterval = () => {
      clearInterval(interval)
      interval = window.setInterval(
        updateIndex,
        currentIndex >= 4 && currentIndex <= 8 ? 100 : 250,
      )
    }

    updateInterval()

    return () => {
      clearInterval(interval)
    }
  }, [currentIndex])

  return (
    <div
      className={
        'fixed inset-0 z-[9999] flex items-center justify-center bg-gray-100'
      }
    >
      <div className={'flex flex-col items-center'}>
        <img
          src={images[currentIndex]}
          alt={`Loading ${currentIndex + 1}`}
          width={150}
          style={{ height: '211px' }}
        />
      </div>
    </div>
  )
}

export default Loading
