import { useEffect, useRef } from 'react'
import { IoClose } from 'react-icons/io5'

type ModalProps = {
  title: string
  onSubmit?: () => void
  isModalOpen: boolean
  toggleModal: () => void
  children: React.ReactNode
  triggerRef?: React.RefObject<HTMLElement | null>
}

const Modal = ({
  title,
  children,
  isModalOpen,
  onSubmit,
  toggleModal,
  triggerRef,
}: ModalProps) => {
  const previousFocusRef = useRef<HTMLElement | null>(null)

  useEffect(() => {
    if (isModalOpen) {
      previousFocusRef.current = document.activeElement as HTMLElement
    } else if (previousFocusRef.current || triggerRef?.current) {
      const elementToFocus = triggerRef?.current || previousFocusRef.current
      elementToFocus?.focus()
    }
  }, [isModalOpen, triggerRef])

  useEffect(() => {
    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isModalOpen) {
        toggleModal()
      }
    }

    document.addEventListener('keydown', handleEscapeKey)
    return () => {
      document.removeEventListener('keydown', handleEscapeKey)
    }
  }, [isModalOpen, toggleModal])

  return (
    <div>
      {isModalOpen && (
        <div
          id={'default-modal'}
          tabIndex={-1}
          aria-hidden={'true'}
          className={
            'fixed inset-0 z-50 flex size-full items-center justify-center bg-black/50'
          }
        >
          <div className={'relative max-h-full w-full max-w-2xl p-4'}>
            <form onSubmit={onSubmit}>
              <div
                className={
                  'relative rounded-lg bg-white shadow-sm dark:bg-gray-700'
                }
              >
                <div
                  className={
                    'flex items-center justify-between rounded-t border-b border-gray-200 p-4 md:p-5 dark:border-gray-600'
                  }
                >
                  <h3
                    className={
                      'text-xl font-semibold text-gray-900 dark:text-white'
                    }
                  >
                    {title}
                  </h3>
                  <button
                    onClick={toggleModal}
                    type={'button'}
                    className={
                      'ms-auto inline-flex size-8 items-center justify-center rounded-lg bg-transparent text-sm text-gray-400 hover:bg-gray-200 hover:text-gray-900 dark:hover:bg-gray-600 dark:hover:text-white'
                    }
                  >
                    <IoClose className={'text-xl'} />
                  </button>
                </div>
                <div>{children}</div>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  )
}

export default Modal
