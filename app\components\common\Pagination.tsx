import Pagination from '@mui/material/Pagination'
import React from 'react'
import variables from '../../theme/variables'

type PaginationComponentProps = {
  totalItems: number
  itemsPerPage?: number
  currentPage?: number
  onPageChange: (page: number) => void
}

const PaginationComponent: React.FC<PaginationComponentProps> = ({
  totalItems,
  itemsPerPage = 10,
  currentPage = 1,
  onPageChange,
}) => {
  const totalPages = Math.ceil(totalItems / itemsPerPage)

  const handleChange = (_: React.ChangeEvent<unknown>, page: number) => {
    onPageChange(page)
  }

  return (
    <div className={'mt-8 flex justify-center'}>
      <Pagination
        count={totalPages}
        page={currentPage}
        onChange={handleChange}
        sx={{
          '& .MuiPaginationItem-root': {
            color: variables.gray900,
            '&:hover': {
              textDecoration: 'underline',
              color: variables.gray900,
              backgroundColor: variables.green50,
            },
          },
          '& .Mui-selected': {
            color: variables.gray900,
            backgroundColor: variables.green100 + ' !important',
            '&:hover': {
              textDecoration: 'underline',
            },
          },
          '& .MuiPaginationItem-previousNext': {
            color: variables.gray900,
          },
        }}
      />
    </div>
  )
}

export default PaginationComponent
