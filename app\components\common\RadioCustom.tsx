import { styled } from '@mui/system'
import React from 'react'
import variables from '../../theme/variables'
import { Radio } from './MaterialUI'

const CustomRadio = styled(Radio)(({ size }) => {
  let dimensions
  switch (size) {
    case 'small':
      dimensions = '16px'
      break
    case 'medium':
      dimensions = '20px'
      break
    default:
      dimensions = '20px'
  }

  return {
    width: dimensions,
    height: dimensions,
    color: variables.gray600,
    '&.Mui-checked': {
      color: 'white',
      '& .MuiSvgIcon-root': {
        fontSize: dimensions,
        backgroundColor: variables.green600,
        borderRadius: '50%',
      },
    },
    '&.Mui-disabled': {
      color: variables.gray400,
      '& .MuiSvgIcon-root': {
        color: variables.gray400,
      },
    },
  }
})

type RadioCustomProps = {
  label: string
  checked: boolean
  onChange: () => void
  size?: 'small' | 'medium'
  disabled?: boolean
}

const RadioCustom: React.FC<RadioCustomProps> = ({
  label,
  checked,
  onChange,
  size = 'medium',
  disabled = false,
}) => {
  const textSizeClass = size === 'small' ? 'text-sm' : 'text-base'

  return (
    <label
      className={`-mb-[2px] flex cursor-pointer items-center space-x-2 ${disabled ? 'cursor-not-allowed' : ''}`}
    >
      <CustomRadio
        checked={checked}
        className={'!p-0'}
        onChange={onChange}
        size={size}
        disabled={disabled}
      />
      <span
        className={`${textSizeClass} ${disabled ? 'text-gray-400' : 'text-gray-900'} whitespace-nowrap`}
      >
        {label}
      </span>
    </label>
  )
}

export default RadioCustom
