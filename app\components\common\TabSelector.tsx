import React, { useEffect, useState } from 'react'
import { useLocation, useNavigate } from 'react-router'
import variables from '../../theme/variables'
import { Tab, Tabs } from './MaterialUI'

type Tab = {
  key: string
  label: string
  route: string
}

type TabSelectorProps = {
  tabs: Tab[]
}

const TabSelector: React.FC<TabSelectorProps> = ({ tabs }) => {
  const [selectedTab, setSelectedTab] = useState(0)
  const navigate = useNavigate()
  const location = useLocation()

  useEffect(() => {
    const currentTabIndex = tabs.findIndex((tab) => {
      return (
        location.pathname === tab.route ||
        location.pathname === `${tab.route}/` ||
        (location.pathname.startsWith(tab.route) &&
          location.pathname[tab.route.length] === '/')
      )
    })
    if (currentTabIndex !== -1) {
      setSelectedTab(currentTabIndex)
    }
  }, [location.pathname, tabs])

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setSelectedTab(newValue)
    const selectedRoute = tabs[newValue].route
    navigate(selectedRoute)
  }

  return (
    <Tabs
      className={'mt-6'}
      value={selectedTab}
      onChange={handleTabChange}
      sx={{
        borderBottom: `1px solid ${variables.gray200}`,
        '& .MuiTabs-indicator': {
          backgroundColor: variables.green600,
          height: '4px',
          width: '100%',
        },
        '& .Mui-selected': { color: `${variables.green600} !important` },
        '& .MuiTab-root': {
          color: variables.gray600,
          '&:hover': {
            color: 'black',
            backgroundColor: variables.gray50,
            borderBottom: `4px solid ${variables.green600}`,
          },
        },
      }}
    >
      {tabs.map((tab, index) => (
        <Tab
          key={tab.key}
          label={tab.label}
          value={index}
          sx={{
            fontSize: '16px',
            fontWeight: '600',
          }}
        />
      ))}
    </Tabs>
  )
}

export default TabSelector
