import React from 'react'
import { DOWNLOAD_LIST_TABLE } from '../../constants'
import { truncateText } from '../../helper/helper'

type TableProps = {
  data: {
    id: string | number
    date1: string
    date2: string
    title: string
    status:
      | typeof DOWNLOAD_LIST_TABLE.error
      | typeof DOWNLOAD_LIST_TABLE.done
      | typeof DOWNLOAD_LIST_TABLE.creating
    downloadUrl?: string
  }[]
  onDownload?: (id: string | number) => void
}

const Table: React.FC<TableProps> = ({ data, onDownload }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case DOWNLOAD_LIST_TABLE.error:
        return 'text-red-700 px-2 py-[6px]'
      case DOWNLOAD_LIST_TABLE.done:
        return 'text-green-600 px-2 py-[6px] bg-green-100 rounded'
      case DOWNLOAD_LIST_TABLE.creating:
        return 'text-gray-600 px-2 py-[6px] bg-gray-100 rounded'
      default:
        return 'text-gray-600 px-2 py-[6px] bg-gray-100 rounded'
    }
  }

  const columnConfig = [
    { header: '出力日時', width: '200px' },
    { header: '対象記号', width: '440px' },
    { header: '保存期間', width: '160px' },
    { header: 'ステータス', width: '160px' },
    { header: 'ダウンロード', width: '160px' },
  ]

  return (
    <div className={'max-h-[700px] min-w-[1120px] overflow-x-auto'}>
      <table className={'min-w-full border-collapse text-sm text-gray-900'}>
        <thead>
          <tr className={'border-b bg-gray-50'}>
            {columnConfig.map((col, index) => (
              <th
                key={index}
                className={'h-[40px] pl-4 text-left font-normal'}
                style={{ width: col.width }}
              >
                {col.header}
              </th>
            ))}
          </tr>
        </thead>
        <tbody className={'divide-y divide-gray-200 border-b'}>
          {data.map((row) => (
            <tr key={row.id} className={'hover:bg-gray-50'}>
              {[
                { content: row.date1, width: columnConfig[0].width },
                {
                  content: (
                    <span title={row.title}>{truncateText(row.title, 20)}</span>
                  ),
                  width: columnConfig[1].width,
                },
                {
                  content: row.date2,
                  width: columnConfig[2].width,
                  className: 'pr-4',
                },
                {
                  content: (
                    <span className={getStatusColor(row.status)}>
                      {row.status}
                    </span>
                  ),
                  width: columnConfig[3].width,
                },
                {
                  content:
                    row.status === DOWNLOAD_LIST_TABLE.done && row.downloadUrl ?
                      <button
                        onClick={() => onDownload?.(row.id)}
                        className={
                          'text-green-600 underline hover:text-green-800'
                        }
                      >
                        ダウンロード
                      </button>
                    : null,
                  width: columnConfig[4].width,
                },
              ].map((cell, index) => (
                <td
                  key={index}
                  className={`h-[40px] pl-4 ${cell.className ?? ''}`}
                  style={{ width: cell.width }}
                >
                  {cell.content}
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )
}

export default Table
