import { forwardRef, useEffect, useRef, useState } from 'react'
import type { HTMLTableauVizElement } from '../tableau/TableauViz'
import TableauViz from '../tableau/TableauViz'

type TableauProps = {
  src?: string
  height?: string
  marginTop?: string
  onFiltersChange?: (filters: any[]) => void
  filters?: Record<string, any>
  data?: (data: any[]) => void
  isHidden?: boolean
  isData?: boolean
  storageKey?: string
}

const Tableau = forwardRef<HTMLTableauVizElement, TableauProps>(
  (
    {
      src,
      height,
      marginTop,
      onFiltersChange,
      filters,
      data,
      isHidden = false,
      isData = false,
      storageKey,
    },
    ref,
  ) => {
    const [tableauFilters, setTableauFilters] = useState<any[]>([])
    const tableauRef = useRef<HTMLTableauVizElement>(null)

    useEffect(() => {
      onFiltersChange?.(tableauFilters)
    }, [tableauFilters, onFiltersChange])

    const handleFiltersChange = (newFilters: any[]) => {
      setTableauFilters(newFilters)
    }

    return src ?
        <div
          style={
            isHidden ?
              { opacity: 0, width: 0, height: 0, overflow: 'hidden' }
            : {}
          }
        >
          <TableauViz
            ref={ref || tableauRef}
            src={src}
            height={height}
            marginTop={marginTop}
            onFiltersChange={handleFiltersChange}
            filters={filters}
            dataSheet={data}
            isData={isData}
            storageKey={storageKey}
          />
        </div>
      : <div
          className={
            'mt-4 flex h-60 items-center justify-center bg-green-100 text-center text-4xl text-green-400'
          }
        >
          tableauで作成した表が入ります
        </div>
  },
)

export default Tableau
