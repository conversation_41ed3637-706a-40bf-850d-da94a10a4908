import { useEffect } from 'react'
import { Outlet } from 'react-router'

type TitleWrapperProps = {
  titleCustom?: string
  children: React.ReactNode
}

export function TitleWrapper({ titleCustom, children }: TitleWrapperProps) {
  useEffect(() => {
    if (titleCustom) document.title = titleCustom
  }, [titleCustom])

  return <>{children || <Outlet />}</>
}

export function withTitle(Component: React.ComponentType<any>, title?: string) {
  return function WrappedComponent(props: any) {
    return (
      <TitleWrapper titleCustom={title}>
        <Component {...props} />
      </TitleWrapper>
    )
  }
}

export function useTitle(title: string) {
  useEffect(() => {
    if (title) document.title = title
  }, [title])
}
