import { useState } from 'react'

const SegmentedControl = ({
  options,
  onChange,
}: {
  options: { label: string; value: string }[]
  onChange: (value: string) => void
}) => {
  const [selected, setSelected] = useState(options[0].value)

  const handleSelect = (value: string) => {
    setSelected(value)
    onChange(value)
  }

  return (
    <div
      className={
        'inline-flex overflow-hidden rounded-xl border border-gray-300'
      }
    >
      {options.map((option) => (
        <span
          key={option.value}
          onClick={() => handleSelect(option.value)}
          className={`inline-flex min-h-[26px] min-w-[60px] cursor-pointer select-none items-center justify-center text-xs-bold transition-colors duration-300
            ${selected === option.value ? 'bg-green-600 text-white' : 'bg-white text-gray-300'}`}
        >
          {option.label}
        </span>
      ))}
    </div>
  )
}

export default SegmentedControl
