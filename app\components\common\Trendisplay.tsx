import variables from '../../theme/variables'
import { TrendingDownIcon, TrendingUpIcon } from './Icon'

type TrendDisplayProps = {
  currentValue?: string
  trendValue?: string
  previousValue?: string
  unit?: string
  isNegativeTrend?: boolean
  isCenter?: boolean
}

const TrendDisplay: React.FC<TrendDisplayProps> = ({
  currentValue,
  trendValue,
  previousValue,
  unit = '',
  isNegativeTrend = true,
  isCenter = false,
}) => {
  const isNullValue = (value?: string): boolean => {
    const nullValues = new Set(['null', 'NULL', 'Null', '%null%'])
    return !value || nullValues.has(value)
  }

  const hasNullValue =
    isNullValue(currentValue) ||
    isNullValue(trendValue) ||
    isNullValue(previousValue)

  const formatValue = (value?: string) => {
    if (hasNullValue) {
      return '-'
    }
    if (!value) {
      return '0'
    }
    if (!value.startsWith('+') && !value.startsWith('-')) {
      return `＋${value}`
    }
    return value.replace('+', '＋').replace('-', '−')
  }

  const getTrendDetails = (value?: string) => {
    if (hasNullValue || !value) {
      return { color: variables.gray600, icon: null }
    }
    const isNegative = value.startsWith('-')
    const color =
      isNegative === isNegativeTrend ? variables.green600 : variables.red700
    const Icon = isNegative ? TrendingDownIcon : TrendingUpIcon
    return { color, icon: <Icon fill={color} /> }
  }

  return (
    <div className={`text-right ${isCenter ? 'text-center' : ''}`}>
      <div
        className={`flex ${isCenter ? 'justify-center' : 'justify-end'} gap-2 text-3xl font-bold`}
      >
        <span>{hasNullValue ? '-' : currentValue}</span>
        <span className={'flex items-end text-lg-bold'}>{unit}</span>
        {hasNullValue ? null : (
          <span
            className={'flex items-center gap-1 text-lg-bold'}
            style={{ color: getTrendDetails(trendValue).color }}
          >
            {formatValue(trendValue)}
            {getTrendDetails(trendValue).icon}
          </span>
        )}
      </div>
      <p
        className={`${isCenter ? 'text-center' : ''} mt-2 text-sm text-gray-600`}
      >
        前年度： {hasNullValue ? '-' : previousValue} {unit}
      </p>
    </div>
  )
}

export default TrendDisplay
