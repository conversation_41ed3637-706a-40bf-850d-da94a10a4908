import { styled } from '@mui/material/styles'
import React, { useEffect, useRef, useState } from 'react'
import { createPortal } from 'react-dom'
import title from '../../constants/title'
import { truncateText } from '../../helper/helper'
import { getUnions } from '../../services/userService'
import {
  Autocomplete,
  Popper,
  TextField,
  Tooltip,
  autocompleteClasses,
} from './MaterialUI'

type UnionSelectModalProps = {
  open: boolean
  onClose: () => void
  hasUnion: boolean
  onUnionChange?: (union: any) => void
}

const StyledPopper = styled(Popper)(() => ({
  [`&.${autocompleteClasses.popper}`]: {
    width: '420px !important',
    marginLeft: '0px !important',
    marginTop: '2px !important',
  },
}))

const StyledTextField = styled(TextField)(() => ({
  '& .MuiInputBase-root': {
    height: '40px',
    padding: '0 8px',
  },
  '& .MuiOutlinedInput-input': {
    padding: '0',
    height: '40px',
  },
}))

const UnionSelectModal: React.FC<UnionSelectModalProps> = ({
  open,
  onClose,
  hasUnion,
  onUnionChange,
}) => {
  const [inputValue, setInputValue] = useState('')
  const [openAuto, setOpenAuto] = useState(false)
  const [options, setOptions] = useState<
    Array<{ label: string; emplrCd?: string; customEmplrName?: string }>
  >([])
  const [allUnions, setAllUnions] = useState<
    Array<{ label: string; emplrCd?: string; customEmplrName?: string }>
  >([])
  const [selectedUnion, setSelectedUnion] = useState<any>(null)
  const [isValidSelection, setIsValidSelection] = useState(false)
  const isSelectFromPopperRef = useRef(false)

  useEffect(() => {
    if (open) {
      const unionInfo = localStorage.getItem('union_selected_info')
      if (unionInfo) {
        try {
          const { value, expire } = JSON.parse(unionInfo)
          if (expire && Date.now() < expire && value) {
            setOptions([value])
            setSelectedUnion(value)
            setInputValue(value.label || '')
          }
        } catch (e) {
          console.error('🚀 ~ useEffect ~ error', e)
        }
      }

      const loadInitialUnions = async () => {
        try {
          const data = await getUnions('')
          const unionOptions =
            data && data.emplrs ?
              data.emplrs.map(
                (union: { emplrCd: string; customEmplrName: string }) => ({
                  ...union,
                  label: `(${union.emplrCd}) ${truncateText(union.customEmplrName, 19)}`,
                }),
              )
            : []
          setAllUnions(unionOptions)
          setOptions(unionOptions)
        } catch (error) {
          console.error('Error fetching initial unions:', error)
          setAllUnions([])
          setOptions([])
        }
      }

      loadInitialUnions()
    }
  }, [open])

  useEffect(() => {
    if (inputValue && inputValue.length && !isSelectFromPopperRef.current) {
      const searchValue = inputValue.toLowerCase().replace(/[()]/g, '')
      const filteredOptions = allUnions.filter((union) => {
        const unionText =
          `${union.emplrCd} ${union.customEmplrName}`.toLowerCase()
        return unionText.includes(searchValue)
      })
      setOptions(filteredOptions)
    } else if (!inputValue || !inputValue.length) {
      setOptions(allUnions)
    }

    isSelectFromPopperRef.current = false
  }, [inputValue, allUnions])

  if (!open) return null
  const handleSet = () => {
    if (selectedUnion) {
      const expire = Date.now() + 24 * 60 * 60 * 1000
      localStorage.setItem(
        'union_selected_info',
        JSON.stringify({ value: selectedUnion, expire }),
      )
      if (typeof onUnionChange === 'function') {
        onUnionChange(selectedUnion)
      }
    }
    onClose()
  }

  const handleChange = (_event: any, value: any) => {
    setSelectedUnion(value)
    if (value && options.some((opt) => opt.emplrCd === value.emplrCd)) {
      isSelectFromPopperRef.current = true
      setInputValue(
        `(${value.emplrCd}) ${truncateText(value.customEmplrName, 19)}`,
      )
      setIsValidSelection(true)
    } else {
      setIsValidSelection(false)
    }
  }

  const handleInputChange = (_event: any, newInputValue: string) => {
    setInputValue(newInputValue)

    const matchingOption = options.find(
      (opt) =>
        `(${opt.emplrCd}) ${truncateText(opt.customEmplrName || '', 19)}` ===
        newInputValue,
    )

    if (!matchingOption) {
      setIsValidSelection(false)
    }
  }

  const modalContent = (
    <div className={'fixed inset-0 z-[9999] flex items-center justify-center'}>
      <button
        type={'button'}
        className={'fixed inset-0 bg-black/30'}
        aria-label={'Close modal'}
        onClick={hasUnion ? onClose : undefined}
      />
      <div
        className={
          'relative flex h-[256px] w-[600px] flex-col rounded-3xl bg-white shadow-lg'
        }
        role={'dialog'}
        aria-modal={'true'}
      >
        <div
          className={
            'mt-[32px] flex flex-1 flex-col items-center justify-center'
          }
        >
          <div
            className={
              'h-6 w-[420px] text-left text-lg font-normal text-gray-900'
            }
          >
            {title.header.unionTitle}
          </div>
          <Autocomplete
            className={
              'mb-10 mt-2 !h-[40px] w-[420px] bg-white text-base focus:outline-none focus:ring-2'
            }
            clearIcon={null}
            freeSolo={true}
            options={options}
            loadingText={'検索中...'}
            noOptionsText={'結果がありません'}
            PopperComponent={StyledPopper}
            componentsProps={{
              popper: {
                style: { zIndex: 9999 },
                placement: 'bottom-start',
              },
            }}
            open={openAuto}
            onOpen={() => setOpenAuto(true)}
            onClose={() => setOpenAuto(false)}
            inputValue={inputValue}
            onInputChange={handleInputChange}
            onFocus={() => {
              setOpenAuto(true)
            }}
            onChange={handleChange}
            value={selectedUnion}
            renderOption={(props, option) => {
              const fullText = `(${option.emplrCd}) ${option.customEmplrName}`
              const shouldShowTooltip = fullText.length > 19

              return shouldShowTooltip ?
                  <Tooltip
                    title={fullText}
                    placement={'right'}
                    arrow={true}
                    PopperProps={{
                      style: { zIndex: 10000 },
                    }}
                  >
                    <li
                      {...props}
                      key={option.emplrCd}
                      style={{ width: '100%' }}
                    >
                      <div className={'w-full truncate'}>{option.label}</div>
                    </li>
                  </Tooltip>
                : <li {...props} key={option.emplrCd} style={{ width: '100%' }}>
                    <div className={'w-full truncate'}>{option.label}</div>
                  </li>
            }}
            renderInput={(params) => {
              const fullText =
                selectedUnion ?
                  `(${selectedUnion.emplrCd}) ${selectedUnion.customEmplrName}`
                : ''
              const shouldShowTooltip = selectedUnion && fullText.length > 19

              return shouldShowTooltip ?
                  <Tooltip
                    title={fullText}
                    placement={'right'}
                    arrow={true}
                    PopperProps={{
                      style: { zIndex: 10000 },
                    }}
                  >
                    <div>
                      <StyledTextField
                        {...params}
                        InputProps={{
                          ...params.InputProps,
                        }}
                      />
                    </div>
                  </Tooltip>
                : <div>
                    <StyledTextField
                      {...params}
                      InputProps={{
                        ...params.InputProps,
                      }}
                    />
                  </div>
            }}
          />
          <button
            type={'button'}
            className={`h-[48px] w-[200px] rounded-xl text-lg font-bold text-white ${
              isValidSelection ? 'bg-green-600' : (
                'cursor-not-allowed bg-gray-300'
              )
            }`}
            disabled={!isValidSelection}
            onClick={handleSet}
          >
            {title.header.unionSubmit}
          </button>
        </div>
      </div>
    </div>
  )

  return typeof document !== 'undefined' ?
      createPortal(modalContent, document.body)
    : null
}

export default UnionSelectModal
