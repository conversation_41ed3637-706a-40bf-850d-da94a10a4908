import { useAuth0 } from '@auth0/auth0-react'
import MenuIcon from '@mui/icons-material/Menu'
import React, { useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { Link, useNavigate } from 'react-router-dom'
import { routeUrl } from '../../configs/appConfig'
import title from '../../constants/title'
import { useUnion } from '../../contexts/UnionContext'
import { logout } from '../../store/authSlice'
import type { RootState } from '../../store/store'
import variables from '../../theme/variables'
import Loading from '../common/Loading'
import { Box, Popover, Typography } from '../common/MaterialUI'
import UnionSelectModal from '../common/UnionSelectModal'

type HeaderProps = {
  isShowMenu: boolean
  isShowText: boolean
}

const Header: React.FC<HeaderProps> = ({ isShowMenu, isShowText }) => {
  const { isAuthenticated, logout: logoutAuth0 } = useAuth0()
  const dispatch = useDispatch()
  const navigate = useNavigate()
  const user = useSelector((state: RootState) => state.auth.user)
  const [anchorEl, setAnchorEl] = React.useState<HTMLButtonElement | null>(null)

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget)
  }

  const handleClose = () => {
    setAnchorEl(null)
  }

  const handleLogout = async () => {
    setIsLoggingOut(true)
    try {
      await logoutAuth0()
      localStorage.removeItem('union_selected_info')
      dispatch(logout())
      await new Promise((resolve) => setTimeout(resolve, 500))
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      setIsLoggingOut(false)
    }
  }

  const [openModal, setOpenModal] = useState(false)
  const [isLoggingOut, setIsLoggingOut] = useState(false)
  const { refreshUnion } = useUnion()

  useEffect(() => {
    if (!isAuthenticated || user?.hierarchyLevel !== 1) return
    const unionInfo = localStorage.getItem('union_selected_info')
    let needShowModal = true
    if (unionInfo) {
      try {
        const { expire } = JSON.parse(unionInfo)
        if (expire && Date.now() < expire) {
          needShowModal = false
        } else {
          localStorage.removeItem('union_selected_info')
        }
      } catch {
        localStorage.removeItem('union_selected_info')
      }
    }
    if (needShowModal) setOpenModal(true)
  }, [isAuthenticated, user?.hierarchyLevel])

  const handleOpenNoticeList = () => {
    navigate(routeUrl.NOTICE.path)
    handleClose()
  }

  const open = Boolean(anchorEl)
  const id = open ? 'simple-popover' : undefined

  const hasUnion = (() => {
    const unionInfo = localStorage.getItem('union_selected_info')
    if (unionInfo) {
      try {
        const { expire } = JSON.parse(unionInfo)
        return expire && Date.now() < expire
      } catch {
        return false
      }
    }
    return false
  })()

  return (
    <header
      className={
        'h-[56px] border-b-[3px] border-green-600 bg-white p-2 text-black'
      }
    >
      <div className={' mx-auto flex items-center justify-between'}>
        <Link to={'/'} className={'text-base font-semibold'}>
          <img
            src={'/maintain-page/images/logo-header.png'}
            alt={'Quick Reference'}
          />
        </Link>
        <nav className={'space-x-4'}>
          {isAuthenticated && isShowMenu && (
            <>
              <Box display={'flex'} alignItems={'center'} gap={'15px'}>
                <Typography className={'text-sm font-normal '}>
                  {' '}
                  {user?.email || user?.name}{' '}
                </Typography>
                {user?.hierarchyLevel === 1 && isShowText && (
                  <Box
                    component={'button'}
                    onClick={() => setOpenModal(true)}
                    className={
                      'h-[32px] w-[80px] items-center justify-between gap-1 rounded-full border-2 border-green-600 text-xs font-semibold text-green-600 hover:border-green-900 hover:text-green-900'
                    }
                  >
                    <span className={'whitespace-nowrap text-xs-bold'}>
                      組合切替え
                    </span>
                  </Box>
                )}
                <Box
                  sx={{ backgroundColor: open ? variables.gray50 : '' }}
                  component={'button'}
                  className={'cursor-pointer rounded-md border border-gray-300'}
                  onClick={handleClick}
                >
                  <MenuIcon sx={{ height: '32px', width: '32px' }} />
                </Box>
              </Box>
              <Popover
                id={id}
                open={open}
                anchorEl={anchorEl}
                onClose={handleClose}
                anchorOrigin={{
                  vertical: 'bottom',
                  horizontal: 'left',
                }}
              >
                <div
                  className={'cursor-pointer p-2 pr-10 hover:bg-gray-100'}
                  onClick={handleOpenNoticeList}
                >
                  <button className={'w-full text-left'}>
                    {title.notice.title}
                  </button>
                </div>

                <div
                  className={
                    'w-full cursor-pointer p-2 pr-10 text-left hover:bg-gray-100'
                  }
                  onClick={handleLogout}
                  aria-label={'Logout'}
                >
                  <button className={'w-full text-left'}>
                    {title.header.logout}
                  </button>
                </div>
              </Popover>
            </>
          )}
        </nav>
        <UnionSelectModal
          open={openModal}
          onClose={() => setOpenModal(false)}
          hasUnion={hasUnion}
          onUnionChange={() => {
            refreshUnion()
          }}
        />
      </div>
      {isLoggingOut && <Loading />}
    </header>
  )
}

export default Header
