import { useEffect, useState } from 'react'
import { Link, useLocation } from 'react-router'
import { NUMBER_DIVIDER_AGENT } from '../../constants'
import type { ROUTER } from '../../constants/sidebarRouter'
import { SIDEBAR_ROUTER } from '../../constants/sidebarRouter'
import variables from '../../theme/variables'
import {
  iconSize,
  KeyboardArrowDown,
  KeyboardArrowLeft,
  KeyboardArrowRight,
  KeyboardArrowUp,
} from '../common/Icon'
import {
  Box,
  Collapse,
  Divider,
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
} from '../common/MaterialUI'

export const Sidebar: React.FC = () => {
  const [openMenus, setOpenMenus] = useState<{ [key: string]: boolean }>({})
  const [isCollapsed, setIsCollapsed] = useState(false)
  const location = useLocation()
  const [number, setNumber] = useState<number | undefined>(undefined)

  const HOME_ACTIVE_ROUTES = ['/notices', '/settings', '/profile']
  const SUB_ROUTER_HOME_ACTIVE = 2

  useEffect(() => {
    const newOpenMenus: { [key: string]: boolean } = {}
    Object.entries(SIDEBAR_ROUTER).forEach(([key, route]: [string, ROUTER]) => {
      if (
        (route.pathname &&
          (location.pathname === route.pathname ||
            location.pathname.startsWith(route.pathname))) ||
        (route.pathname === '/' && location.pathname.startsWith('/notices'))
      ) {
        if (location.pathname === route.pathname) {
          setNumber(route?.number)
        } else if (route.children) {
          const matchingChild = route.children.find(
            (child) =>
              child.pathname && location.pathname.startsWith(child.pathname),
          )
          if (matchingChild) {
            setNumber(route?.number)
          }
        }
        newOpenMenus[key] = true
      }
    })

    setOpenMenus(newOpenMenus)
  }, [location.pathname])

  const handleToggle = (key: string, route: ROUTER) => {
    if (route.children) {
      if (isCollapsed) {
        setIsCollapsed(false)
        setOpenMenus((prev) => ({ ...prev, [key]: true }))
      } else {
        const hasActiveChild = route.children.some(
          (child) =>
            child.pathname && location.pathname.startsWith(child.pathname),
        )
        if (!hasActiveChild) {
          setOpenMenus((prev) => {
            const newOpenMenus: { [key: string]: boolean } = {}
            if (prev[key]) {
              newOpenMenus[key] = false
            } else {
              newOpenMenus[key] = true
            }
            return newOpenMenus
          })
        }
      }
    } else if (isCollapsed) {
      setIsCollapsed(false)
    }
  }

  const handleResize = () => {
    const isCollapsed = window.innerWidth < 768
    setIsCollapsed(isCollapsed)
    if (isCollapsed) {
      localStorage.removeItem('isSidebarOpen')
    } else {
      localStorage.setItem('isSidebarOpen', 'true')
    }
  }

  useEffect(() => {
    handleResize()

    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [])

  const toggleSidebar = () => {
    setIsCollapsed((prev) => {
      const newState = !prev
      if (newState) {
        localStorage.removeItem('isSidebarOpen')
      } else {
        localStorage.setItem('isSidebarOpen', 'true')
      }
      return newState
    })
  }

  const getColor = (
    isActiveText?: boolean,
    isChildActive?: boolean,
    defaultColor = 'white',
  ): string => {
    if (isActiveText && isChildActive) {
      return variables.gray900 + ' !important'
    } else if (isActiveText) {
      return variables.drawerBg + ' !important'
    } else if (isChildActive) {
      return variables.gray900 + ' !important'
    } else {
      return defaultColor
    }
  }

  const getColorIcon = (
    isActiveText?: boolean,
    isChildActive?: boolean,
  ): string => {
    if (isChildActive) {
      return variables.gray900
    }
    if (isActiveText) {
      return variables.green600
    }
    return 'white'
  }

  const [isTallEnough, setIsTallEnough] = useState(true)

  useEffect(() => {
    const checkHeight = () => {
      setIsTallEnough(window.innerHeight > 1024)
    }

    checkHeight()
    window.addEventListener('resize', checkHeight)
    return () => window.removeEventListener('resize', checkHeight)
  }, [])

  const [isScrollable, setIsScrollable] = useState(false)
  useEffect(() => {
    const handleResize = () => {
      const isNowScrollable = window.innerHeight < 1024
      setIsScrollable(isNowScrollable)

      if (!isNowScrollable) {
        const sidebarElement = document.querySelector('.MuiDrawer-paper')
        if (sidebarElement) {
          sidebarElement.scrollTop = 0
        }
      }
    }

    handleResize()

    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [])

  return (
    <Box display={'flex'}>
      <Drawer
        sx={{
          '& .MuiDrawer-paper': {
            position: 'relative',
            width: !isCollapsed ? '240px' : '72px',
            borderRightWidth: '0px',
            transition: 'width 0.3s ease-in-out',
            overflowY: isScrollable ? 'auto' : 'hidden',
            overflowX: 'hidden',
            paddingLeft: '12px',
            paddingTop: '22px',
            cursor: 'pointer',
            height: isTallEnough ? 'auto' : '100%',
            boxShadow: `inset 0 24px 0 0 ${variables.drawerBg}, inset 30px 0 0 0 ${variables.drawerBg}, inset 0 -90px 0 0 ${variables.drawerBg}`,
            '&::-webkit-scrollbar': {
              width: '0px',
            },
          },
        }}
        variant={'permanent'}
        anchor={'left'}
      >
        <Box
          component={'button'}
          onClick={(e) => {
            if (e.target === e.currentTarget) {
              setIsCollapsed((prev) => !prev)
            }
          }}
          sx={{
            width: '100%',
            border: 'none',
            background: 'none',
            padding: 0,
            cursor: 'pointer',
          }}
        >
          <List sx={{ position: 'relative', paddingTop: '2px' }}>
            {Object.entries(SIDEBAR_ROUTER).map(
              ([key, route]: [string, ROUTER]) => {
                const isChildActive = route.children?.some(
                  (child) =>
                    child.pathname &&
                    location.pathname.startsWith(child.pathname),
                )

                const isActive = (() => {
                  const isExactMatch = location.pathname === route.pathname
                  const isSubRouteOfHome =
                    route.pathname === '/' &&
                    HOME_ACTIVE_ROUTES.some((path) =>
                      location.pathname.startsWith(path),
                    )

                  return isExactMatch || isChildActive || isSubRouteOfHome
                })()

                const isActiveText = (() => {
                  const isExactMatch = location.pathname === route.pathname
                  const isChildRoute =
                    route.pathname !== '/' &&
                    route.pathname &&
                    location.pathname.startsWith(route.pathname)
                  const isSubRouteOfHome =
                    route.pathname === '/' &&
                    HOME_ACTIVE_ROUTES.some((path) =>
                      location.pathname.startsWith(path),
                    )

                  return isExactMatch || isChildRoute || isSubRouteOfHome
                })()

                const isSubRouteOfHome = HOME_ACTIVE_ROUTES.some(
                  (path) =>
                    path === location.pathname ||
                    location.pathname.startsWith(path),
                )

                const showTopCorner =
                  (isSubRouteOfHome &&
                    route.number === SUB_ROUTER_HOME_ACTIVE) ||
                  (!isSubRouteOfHome &&
                    typeof number === 'number' &&
                    number + 1 === route.number)

                const showBottomCorner =
                  !isSubRouteOfHome &&
                  typeof number === 'number' &&
                  number - 1 === route.number

                return (
                  <div key={`sidebar-item-${key}-${route.number}`}>
                    <ListItem disablePadding={true}>
                      {route.label === '' ?
                        <ListItemButton
                          component={Link}
                          onClick={
                            route.number === NUMBER_DIVIDER_AGENT ?
                              toggleSidebar
                            : undefined
                          }
                          to={location.pathname}
                          disableRipple={true}
                          sx={{
                            height:
                              route.number === NUMBER_DIVIDER_AGENT ?
                                isTallEnough ? '100vh'
                                : '250px'
                              : '0px',
                            position: 'relative',
                            backgroundColor:
                              isActive ? 'white !important' : (
                                variables.drawerBg
                              ),
                            fontWeight: '500',
                            borderRadius: (() => {
                              if (showBottomCorner && !isCollapsed) {
                                return '16px 0px 16px 16px'
                              }
                              if (showTopCorner && !isCollapsed) {
                                return '16px 16px 0px 16px'
                              }
                              if (!isActive) {
                                return '16px 0px 0px 16px'
                              }
                              return (
                                  route.children &&
                                    openMenus[key] &&
                                    !isCollapsed
                                ) ?
                                  '16px 0px 0px 0px'
                                : '16px 0px 0px 16px'
                            })(),
                            color: getColor(isActiveText, isChildActive),
                            paddingTop: '18px',
                            paddingBottom: '18px',
                            '&:hover': {
                              backgroundColor:
                                !isActive ?
                                  `${variables.drawerBg} !important`
                                : '',
                              borderRadius: (() => {
                                if (showBottomCorner) {
                                  return '16px 0px 16px 16px'
                                }
                                if (showTopCorner) {
                                  return '16px 16px 0px 16px'
                                }
                                return (
                                  isActive ?
                                    (
                                      route.children &&
                                      openMenus[key] &&
                                      !isCollapsed
                                    ) ?
                                      '16px 0px 0px 0px'
                                    : '16px 0px 0px 16px'
                                  : '16px 0px 0px 16px'
                                )
                              })(),
                            },
                          }}
                        >
                          {' '}
                          {route.number === NUMBER_DIVIDER_AGENT ?
                            ''
                          : <Box
                              sx={{
                                width: isCollapsed ? '40px' : '100%',
                                height: '1px',
                                backgroundColor: 'white',
                                paddingLeft: '4px',
                                marginRight: '-20px',
                              }}
                            />
                          }
                        </ListItemButton>
                      : <ListItemButton
                          component={Link}
                          to={route.pathname ?? '#'}
                          sx={{
                            position: 'relative',
                            backgroundColor:
                              isActive ? 'white !important' : (
                                variables.drawerBg
                              ),
                            fontWeight: isActiveText ? '600' : '500',
                            borderRadius: (() => {
                              if (showBottomCorner && !isCollapsed) {
                                return '16px 0px 16px 16px'
                              }
                              if (showTopCorner && !isCollapsed) {
                                return '16px 16px 0px 16px'
                              }
                              if (!isActive) {
                                return '16px 0px 0px 16px'
                              }
                              return (
                                  route.children &&
                                    openMenus[key] &&
                                    !isCollapsed
                                ) ?
                                  '16px 0px 0px 0px'
                                : '16px 0px 0px 16px'
                            })(),
                            color: getColor(isActiveText, isChildActive),
                            paddingTop: '16px',
                            paddingBottom: '16px',
                            paddingLeft: '12px',
                            transition: 'all 0.15s ease-in-out',
                            '&:hover': {
                              backgroundColor:
                                !isActive ?
                                  `${variables.green500} !important`
                                : '',
                              borderRadius: (() => {
                                if (showBottomCorner) {
                                  return '16px 0px 16px 16px'
                                }
                                if (showTopCorner) {
                                  return '16px 16px 0px 16px'
                                }
                                return (
                                  isActive ?
                                    (
                                      route.children &&
                                      openMenus[key] &&
                                      !isCollapsed
                                    ) ?
                                      '16px 0px 0px 0px'
                                    : '16px 0px 0px 16px'
                                  : '16px 0px 0px 16px'
                                )
                              })(),
                            },
                          }}
                          className={
                            (
                              typeof number === 'number' &&
                              number - 1 === route.number
                            ) ?
                              'relative'
                            : ''
                          }
                          onClick={() => handleToggle(key, route)}
                        >
                          {route.icon && (
                            <ListItemIcon
                              sx={{
                                color: getColor(isActiveText, isChildActive),
                                minWidth: '24px',
                                fontSize: '24px',
                              }}
                            >
                              {route.icon({
                                fill: getColorIcon(isActiveText, isChildActive),
                                width: iconSize.medium,
                                height: iconSize.medium,
                              })}
                            </ListItemIcon>
                          )}

                          <p
                            style={{
                              marginLeft: '8px',
                              opacity: isCollapsed ? 0 : 1,
                              transition: 'opacity 0.3s ease-in-out',
                              width: isCollapsed ? 0 : 'auto',
                              overflow: 'hidden',
                              whiteSpace: 'nowrap',
                            }}
                          >
                            {route.label}
                          </p>
                          {!isCollapsed && route.children ?
                            <div>
                              {openMenus[key] ?
                                <KeyboardArrowUp
                                  width={iconSize.medium}
                                  height={iconSize.medium}
                                  fill={(() => {
                                    if (isChildActive) {
                                      return variables.gray300
                                    }
                                    if (isActiveText) {
                                      return variables.gray300
                                    }
                                    return 'white'
                                  })()}
                                />
                              : <KeyboardArrowDown
                                  width={iconSize.medium}
                                  height={iconSize.medium}
                                  fill={
                                    isActiveText ? variables.green600 : 'white'
                                  }
                                />
                              }
                            </div>
                          : null}
                        </ListItemButton>
                      }
                    </ListItem>

                    {route.children && (
                      <Collapse
                        sx={{
                          backgroundColor:
                            isActive ? 'white !important' : 'transparent',
                          fontWeight: isActive ? '600' : 'normal',
                          borderRadius: isActive ? '0px 0px 0px 16px' : '0px',
                          opacity: isCollapsed ? 0 : 1,
                          transition: 'height 0.15s ease-in-out',
                          width: isCollapsed ? 0 : 'auto',
                          overflow: 'hidden',
                          whiteSpace: 'nowrap',
                        }}
                        in={!!openMenus[key] && !isCollapsed}
                        timeout={200}
                      >
                        <List component={'div'} disablePadding={true}>
                          {route.children.map((child) => {
                            const isChildActive =
                              child.pathname &&
                              location.pathname.startsWith(child.pathname)
                            return (
                              <div
                                key={`child-${child.pathname || key}-${child.label}`}
                              >
                                <ListItemButton
                                  component={Link}
                                  to={child.pathname ?? '#'}
                                  sx={{
                                    pl: 4,
                                    pb: 2,
                                    backgroundColor:
                                      isChildActive ? 'white !important' : (
                                        'transparent'
                                      ),
                                    color:
                                      isChildActive ?
                                        `${variables.primaryColor} !important`
                                      : variables.gray900,
                                    borderRadius:
                                      isChildActive ? '16px 0px 0px 16px' : (
                                        '0px'
                                      ),
                                    height: '40px',
                                    minHeight: '40px',
                                    '&:hover': {
                                      color: `${variables.drawerBg} !important`,
                                      backgroundColor: 'white !important',
                                      textDecoration:
                                        isChildActive ? 'underline' : 'none',
                                    },
                                  }}
                                >
                                  <span
                                    style={{
                                      marginLeft: '20px',
                                      fontWeight: isChildActive ? '600' : '400',
                                      fontSize: '16px',
                                    }}
                                  >
                                    {child.label}
                                  </span>
                                </ListItemButton>
                                {child.hasDivider && (
                                  <Divider
                                    sx={{
                                      my: 1,
                                      backgroundColor: `${variables.gray200}`,
                                      marginLeft: '50px',
                                      mt: '0px',
                                      width: '180px',
                                    }}
                                  />
                                )}
                              </div>
                            )
                          })}
                        </List>
                      </Collapse>
                    )}
                  </div>
                )
              },
            )}
          </List>
        </Box>
      </Drawer>
      <Box
        component={'button'}
        onClick={toggleSidebar}
        sx={{
          backgroundColor: variables.drawerBg,
          color: variables.white50,
          height: '24px',
          borderRadius: '0px 6px 6px 0px',
          cursor: 'pointer',
          display: 'flex',
          position: 'absolute',
          alignItems: 'center',
          width: isCollapsed ? '84px' : '248px',
          zIndex: 9998,
          justifyContent: 'flex-end',
          paddingRight: '5px',
          transition:
            'width 0.3s ease-in-out, background-color 0.3s ease-in-out',
        }}
      >
        <div>
          {isCollapsed ?
            <KeyboardArrowRight />
          : <KeyboardArrowLeft />}
        </div>
      </Box>
    </Box>
  )
}
