import React, {
  forwardRef,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react'
import { useSelector } from 'react-redux'
import type { HTMLTableauVizElement, TableauAPI } from '../../helper/tableau'
import {
  FilterUpdateType,
  // TableauUser,
  applyParametersAndRegularFilters,
  // applySpecialFilters,
  debounce,
  findWorkSheet,
  loadSummaryData,
  loadTableauApi,
  parseFilters,
  setupEventListeners,
  setupMarkSelectionListener,
} from '../../helper/tableau'
import { fetchJwt } from '../../services/tokenTableau'
import type { RootState } from '../../store/store'
import FILTER from '../../constants/filter'

const TableauViz = 'tableau-viz' as unknown as React.ForwardRefExoticComponent<
  {
    src: string
    token?: string
    device?: string
    'hide-tabs'?: boolean
    toolbar?: string
    style?: React.CSSProperties
  } & React.RefAttributes<HTMLTableauVizElement>
>

type TableauEmbedProps = {
  src: string
  height?: string
  marginTop?: string
  onFiltersChange?: (filters: any[]) => void
  filters?: Record<string, any>
  dataSheet?: (data: any[]) => void
  isData?: boolean
  onLoadingChange?: (isLoading: boolean) => void
  storageKey?: string
}

const TableauEmbed = forwardRef<HTMLTableauVizElement, TableauEmbedProps>(
  (
    {
      src,
      height,
      marginTop = '40px',
      onFiltersChange,
      filters,
      dataSheet,
      isData,
      onLoadingChange,
      storageKey,
    },
    ref,
  ) => {
    const internalRef = useRef<HTMLTableauVizElement | null>(null)
    const [token, setToken] = useState('')
    const [uniqueFilters, setUniqueFilters] = useState<any[] | null>(null)
    const [data, setData] = useState<any[] | null>(null)
    const isInitialLoad = useRef(true)
    const user = useSelector((state: RootState) => state.auth.user)
    const [isLoading, setIsLoading] = useState(true)

    // Get union info for hierarchyLevel 1 users
    let unionInfo = null
    if (user?.hierarchyLevel === 1) {
      const union = localStorage.getItem('union_selected_info')
      if (union) {
        try {
          unionInfo = JSON.parse(union).value
        } catch {
          localStorage.removeItem('union_selected_info')
        }
      }
    }
    const [tableauApi, setTableauApi] = useState<TableauAPI | null>(null)
    const eventListenersRef = useRef<{ [key: string]: (event: Event) => void }>(
      {},
    )
    const lastAppliedFiltersRef = useRef<string>('')
    const filtersRef = useRef(filters)

    const propsRef = useRef({
      onFiltersChange,
      dataSheet,
      isData,
      filters,
      onLoadingChange,
      storageKey,
    })

    useEffect(() => {
      propsRef.current = {
        onFiltersChange,
        dataSheet,
        isData,
        filters,
        onLoadingChange,
        storageKey,
      }
    }, [
      onFiltersChange,
      dataSheet,
      isData,
      filters,
      onLoadingChange,
      storageKey,
    ])

    useEffect(() => {
      propsRef.current.onLoadingChange?.(isLoading)
    }, [isLoading])

    const hasSetupListeners = useRef(false)

    const getCurrentRef = useCallback(() => {
      return ref && 'current' in ref ? ref.current : internalRef.current
    }, [ref])

    useEffect(() => {
      if (data) {
        propsRef.current.dataSheet?.(data)
        if (propsRef.current.isData && propsRef.current.storageKey) {
          localStorage.setItem(
            propsRef.current.storageKey,
            JSON.stringify(data),
          )
        }
      }
    }, [data])

    const handleProcessFilters = useCallback(
      async (
        filters: any[],
        FilterType: any,
        onFiltersChange?: (filters: any[]) => void,
        parameters?: any[],
      ) => {
        await parseFilters(
          filters,
          FilterType,
          setUniqueFilters,
          onFiltersChange,
          parameters,
        )
      },
      [],
    )

    const handleSetupMarkSelectionListener = useCallback(
      (viz: HTMLTableauVizElement, TableauEventType: any, SheetType: any) => {
        if (hasSetupListeners.current) return
        setupMarkSelectionListener(viz, TableauEventType, SheetType)
      },
      [],
    )

    // const handleApplySpecialFilters = useCallback(
    //   async (activeSheet: any, user: TableauUser | null) => {
    //     await applySpecialFilters(activeSheet, user, FilterUpdateType)
    //   },
    //   [],
    // )

    const handleApplyParametersAndRegularFilters = useCallback(
      async (
        workbook: any,
        activeSheet: any,
        filters: any[],
        filterValues: Record<string, any>,
      ) => {
        await applyParametersAndRegularFilters(
          workbook,
          activeSheet,
          filters,
          filterValues,
          FilterUpdateType,
        )
      },
      [],
    )

    // Function to apply default filters (emplrId and jigyosyoCd)
    const applyDefaultFilters = useCallback(
      async (activeSheet: any) => {
        if (!activeSheet || !user) return

        console.log('🔧 Applying default filters...')
        const filterPromises = []

        // Apply emplrId filter
        const emplrId = unionInfo?.emplrId || user?.emplrId
        if (emplrId) {
          console.log('🔧 Applying emplrId filter:', emplrId)
          filterPromises.push(
            activeSheet
              .applyFilterAsync(
                FILTER.emplrId,
                [String(emplrId)],
                FilterUpdateType.Replace,
              )
              .catch((error: any) => {
                console.log('⚠️ emplrId filter failed:', error)
              })
          )
        }

        // Apply jigyosyoCd filter for hierarchyLevel 3
        if (user.hierarchyLevel === 3 && user.jigyosyoCd) {
          console.log('🔧 Applying jigyosyoCd filter:', user.jigyosyoCd)
          filterPromises.push(
            activeSheet
              .applyFilterAsync(
                FILTER.jigyosyoCd,
                [user.jigyosyoCd],
                FilterUpdateType.Replace,
              )
              .catch((error: any) => {
                console.log('⚠️ jigyosyoCd filter failed:', error)
              })
          )
        }

        if (filterPromises.length > 0) {
          await Promise.all(filterPromises)
          console.log('✅ Default filters applied')
        }
      },
      [user, unionInfo],
    )

    useEffect(() => {
      if (!tableauApi) {
        setIsLoading(true)
        loadTableauApi()
          .then((api) => {
            setTableauApi(api)
          })
          .catch(() => {
            setIsLoading(false)
          })
      }
    }, [tableauApi])

    const handleLogin = async () => {
      setIsLoading(true)
      try {
        const newToken = await fetchJwt()
        setToken(newToken)
      } catch (error) {
        setIsLoading(false)
      }
    }

    const handleDelayedOperations = useCallback(
      (
        viz: HTMLTableauVizElement | null,
        workbook: any,
        activeSheet: any,
        api: TableauAPI,
      ) => {
        if (!viz || hasSetupListeners.current) return

        hasSetupListeners.current = true
        const { FilterType, SheetType, TableauEventType } = api

        const targetSheet = findWorkSheet(workbook, activeSheet, 'Graph')

        Promise.all([
          targetSheet.getFiltersAsync().catch(() => []),
          workbook.getParametersAsync().catch(() => [] as any[]),
        ])
          .then(([filters, parameters]) => {
            handleProcessFilters(
              filters,
              FilterType,
              propsRef.current.onFiltersChange,
              parameters,
            )
            isInitialLoad.current = false
          })
          .catch(() => {})

        handleSetupMarkSelectionListener(viz, TableauEventType, SheetType)
      },
      [handleProcessFilters, handleSetupMarkSelectionListener],
    )

    const setupInteractionHandlers = useCallback(
      (
        viz: HTMLTableauVizElement,
        workbook: any,
        activeSheet: any,
        api: TableauAPI,
      ) => {
        const clickHandler = () =>
          handleDelayedOperations(viz, workbook, activeSheet, api)

        eventListenersRef.current = setupEventListeners(viz, {
          click: clickHandler,
        })
      },
      [handleDelayedOperations],
    )

    useEffect(() => {
      if (!token || !tableauApi || !user) return

      const loadTableau = async () => {
        const { TableauEventType, FilterType } = tableauApi

        const viz = getCurrentRef()
        if (!viz) return

        if (viz.isInitialized && !isData) return
        viz.token = token
        viz.isLoaded = false
        viz.isInitialized = true

        const firstInteractiveHandler = async () => {
          viz.isLoaded = true

          const workbook = viz.workbook
          if (!workbook) {
            setIsLoading(false)
            return
          }

          const activeSheet = workbook.activeSheet

          try {
            const targetSheet = findWorkSheet(workbook, activeSheet, 'Graph')
            isData &&
              loadSummaryData(targetSheet, setData, propsRef.current.storageKey)

            // Apply default filters (emplrId and jigyosyoCd)
            await applyDefaultFilters(activeSheet)

            setIsLoading(false)

            Promise.all([
              targetSheet.getFiltersAsync().catch(() => []),
              workbook.getParametersAsync().catch(() => [] as any[]),
            ])
              .then(([filters, parameters]) => {
                handleProcessFilters(
                  filters,
                  FilterType,
                  propsRef.current.onFiltersChange,
                  parameters,
                )
                isInitialLoad.current = false
              })
              .catch(() => {})

            setupInteractionHandlers(viz, workbook, activeSheet, tableauApi)
          } catch (error) {
            setIsLoading(false)
          }
        }

        const errorHandler = () => {
          setIsLoading(false)
          viz.isInitialized = false
        }

        eventListenersRef.current = setupEventListeners(viz, {
          [TableauEventType.FirstInteractive]: firstInteractiveHandler,
          [TableauEventType.VizLoadError]: errorHandler,
        })
      }

      loadTableau().catch(() => {
        setIsLoading(false)

        const viz = getCurrentRef()
        if (viz) viz.isInitialized = false
      })

      return () => {
        const viz = getCurrentRef()
        if (viz) {
          setupEventListeners(viz, {})
        }
      }
    }, [
      token,
      getCurrentRef,
      parseFilters,
      setupMarkSelectionListener,
      tableauApi,
      applyDefaultFilters,
      handleProcessFilters,
      isData,
      setupInteractionHandlers,
      user,
    ])

    const debouncedApplyFilters = useMemo(
      () =>
        debounce(async () => {
          const currentElement = getCurrentRef()
          if (!currentElement?.workbook || !user || isInitialLoad.current)
            return

          const currentFilters = uniqueFilters || []
          const filtersSnapshot = JSON.stringify(currentFilters)

          if (
            !currentFilters.length ||
            filtersSnapshot === lastAppliedFiltersRef.current
          ) {
            return
          }

          lastAppliedFiltersRef.current = filtersSnapshot

          setIsLoading(true)
          try {
            const workbook = currentElement.workbook
            const activeSheet = workbook.activeSheet

            if (propsRef.current.filters && currentFilters.length) {
              await handleApplyParametersAndRegularFilters(
                workbook,
                activeSheet,
                currentFilters,
                propsRef.current.filters,
              )
            }
          } catch (error) {
            console.error('Error applying filters:', error)
          } finally {
            setIsLoading(false)
          }
        }, 500),
      [
        uniqueFilters,
        getCurrentRef,
        user,
        applyParametersAndRegularFilters,
        filters,
      ],
    )

    useEffect(() => {
      if (uniqueFilters && !isInitialLoad.current) {
        debouncedApplyFilters()
      }
    }, [uniqueFilters, debouncedApplyFilters])

    useEffect(() => {
      if (isInitialLoad.current || !filters || !filtersRef.current) {
        if (filters && !filtersRef.current) {
          filtersRef.current = filters
        }
        return
      }

      if (JSON.stringify(filters) === JSON.stringify(filtersRef.current)) {
        return
      }

      filtersRef.current = filters
      const viz = getCurrentRef()
      if (!viz?.workbook) return

      setIsLoading(true)

      const workbook = viz.workbook
      const activeSheet = workbook.activeSheet

      workbook
        .getParametersAsync()
        .then((parameters: any[]) => {
          const parameterNames = parameters.map((param) => param.name)

          const parameterFilters: Record<string, any> = {}
          const regularFilters: Record<string, any> = {}

          Object.entries(filters).forEach(([key, value]) => {
            if (parameterNames.includes(key)) {
              parameterFilters[key] = value
            } else {
              regularFilters[key] = value
            }
          })

          const parameterPromises = Object.entries(parameterFilters).map(
            ([name, value]) => workbook.changeParameterValueAsync(name, value),
          )

          return Promise.all(parameterPromises)
            .then(() => activeSheet.getFiltersAsync())
            .then((sheetFilters) =>
              handleApplyParametersAndRegularFilters(
                workbook,
                activeSheet,
                sheetFilters,
                regularFilters,
              ),
            )
        })
        .catch(() => {})
        .finally(() => setIsLoading(false))
    }, [
      filters,
      isInitialLoad,
      getCurrentRef,
      setIsLoading,
      applyParametersAndRegularFilters,
    ])

    useEffect(() => {
      handleLogin()
      return () => {
        const viz = getCurrentRef()
        if (viz) {
          viz.isInitialized = false
          viz.isLoaded = false
        }
      }
    }, [])

    return token && tableauApi ?
        <>
          <TableauViz
            ref={ref || internalRef}
            src={src}
            device={'desktop'}
            hide-tabs={true}
            toolbar={'hidden'}
            style={{
              width: '100%',
              height,
              justifyContent: 'center',
              marginTop,
              overflow: 'hidden',
              opacity: isLoading ? 0.6 : 1,
              transition: 'opacity 0.3s ease-in-out',
            }}
          />
        </>
      : null
  },
)

export { TableauEmbed as default, type HTMLTableauVizElement }
