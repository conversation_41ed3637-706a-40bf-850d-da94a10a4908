import React, { useRef, useState, useEffect } from 'react'
import TableauViz from './TableauViz'
import type { HTMLTableauVizElement } from './TableauViz'

type TableauWebEnhancedProps = {
  src?: string
  height?: string | number
  onFiltersChange?: (filters: any[]) => void
  filters?: Record<string, any>
  dataSheet?: (data: any[]) => void
  isData?: boolean
  onLoadingChange?: (loading: boolean) => void
  storageKey?: string
  exportType?: string
  exportTrigger?: number
  filterKey?: string[]
  markKey?: string[]
  exportTitle?: string
  isSummary?: boolean
}

const TableauWebEnhanced: React.FC<TableauWebEnhancedProps> = ({
  src,
  height,
  onFiltersChange,
  filters,
  dataSheet,
  isData,
  onLoadingChange,
  storageKey,
  // Các props khác không được TableauViz hỗ trợ sẽ bị bỏ qua
  exportType,
  exportTrigger,
  filterKey,
  markKey,
  exportTitle,
  isSummary,
}) => {
  const tableauRef = useRef<HTMLTableauVizElement>(null)
  const [debugInfo, setDebugInfo] = useState<string>('Initializing...')

  // Log khi component mount
  useEffect(() => {
    console.log('🎯 TableauWebEnhanced mounted with props:', {
      src,
      isData,
      storageKey,
      hasDataSheet: !!dataSheet
    })
    setDebugInfo('Component mounted')
  }, [])

  // Wrapper function để log dữ liệu
  const handleDataSheet = (data: any[]) => {
    console.log('🚀 TableauWebEnhanced received data:', data)
    setDebugInfo(`✅ Received ${data.length} rows at ${new Date().toLocaleTimeString()}`)
    dataSheet?.(data)
  }

  // Debug function để log worksheet info
  const debugWorksheets = (ref: HTMLTableauVizElement | null) => {
    if (!ref?.workbook) return

    const workbook = ref.workbook
    const activeSheet = workbook.activeSheet

    console.log('📋 Workbook info:', {
      activeSheet: activeSheet?.name,
      sheetType: activeSheet?.sheetType,
      hasWorksheets: !!activeSheet?.worksheets,
      worksheetCount: activeSheet?.worksheets?.length || 0
    })

    if (activeSheet?.worksheets) {
      console.log('📄 Available worksheets:',
        activeSheet.worksheets.map((ws: any) => ({
          name: ws.name,
          type: ws.sheetType
        }))
      )
    }
  }

  // Effect để debug khi tableau ref thay đổi
  useEffect(() => {
    if (tableauRef.current && isData) {
      const timer = setTimeout(() => {
        debugWorksheets(tableauRef.current)
      }, 2000) // Đợi 2 giây để tableau load xong

      return () => clearTimeout(timer)
    }
  }, [isData])

  const handleLoadingChange = (loading: boolean) => {
    console.log('🔄 TableauWebEnhanced loading:', loading)
    setDebugInfo(loading ? '⏳ Loading...' : '🔄 Load complete')
    onLoadingChange?.(loading)
  }

  if (!src) {
    return (
      <div
        className={
          'mt-4 flex h-60 items-center justify-center bg-green-100 text-center text-4xl text-green-400'
        }
      >
        tableauで作成した表が入ります
      </div>
    )
  }

  return (
    <>
      {/* Debug info khi isData = true */}
      {isData && (
        <div style={{
          position: 'fixed',
          top: '10px',
          right: '10px',
          background: 'rgba(0,0,0,0.8)',
          color: 'white',
          padding: '10px',
          borderRadius: '4px',
          fontSize: '12px',
          zIndex: 9999
        }}>
          <div>🔍 TableauWebEnhanced Debug</div>
          <div>isData: {isData ? 'true' : 'false'}</div>
          <div>src: {src}</div>
          <div>storageKey: {storageKey}</div>
          <div>{debugInfo}</div>
        </div>
      )}

      <div
        className={`flex justify-center overflow-hidden ${isSummary ? '' : 'mt-10'}`}
        style={{
          opacity: isData ? 0 : 1,
          width: isData ? 0 : '100%',
          height: isData ? 0 : 'auto',
          overflow: isData ? 'hidden' : 'visible',
          position: isData ? 'absolute' : 'relative',
          left: isData ? '-9999px' : 'auto',
        }}
      >
        <TableauViz
          ref={tableauRef}
          src={src}
          height={typeof height === 'number' ? `${height}px` : height}
          onFiltersChange={onFiltersChange}
          filters={filters}
          dataSheet={handleDataSheet}
          isData={isData}
          onLoadingChange={handleLoadingChange}
          storageKey={storageKey}
        />
      </div>
    </>
  )
}

export default TableauWebEnhanced
