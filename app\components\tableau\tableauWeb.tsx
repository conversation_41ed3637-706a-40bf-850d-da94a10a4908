import { EXPORT_TYPES } from '../../constants'
import FILTER from '../../constants/filter'
import { TABLEAU_STORAGE_KEYS } from '../../constants/tableauLinks'
import { getMemberLabel } from '../../helper/helper'
import type { TableauAPI } from '../../helper/tableau'
import {
  applyParametersAndRegularFilters,
  debounce,
  FilterUpdateType,
  findWorkSheet,
  loadSummaryData,
  loadTableauApi,
  parseFilters,
} from '../../helper/tableau'
import { getBID } from '../../services/snowflakeService'
import { downloadCsv, downloadPdf } from '../../services/tableauService'
import { fetchJwt } from '../../services/tokenTableau'
import type { RootState } from '../../store/store'

import { useEffect, useMemo, useRef, useState } from 'react'
import { useSelector } from 'react-redux'
import { getFormattedDate } from '../../helper/helper'

type TableauWebProps = {
  src?: string
  height?: string | number
  onFiltersChange?: (filters: any[]) => void
  filters?: Record<string, any>
  dataSheet?: (data: any[]) => void
  isData?: boolean
  onLoadingChange?: (loading: boolean) => void
  storageKey?: string
  exportType?: string
  exportTrigger?: number
  filterKey?: string[]
  markKey?: string[]
  exportTitle?: string
  isSummary?: boolean
}

const TableauWeb: React.FC<TableauWebProps> = ({
  src,
  onFiltersChange,
  filters,
  filterKey,
  dataSheet,
  isData,
  onLoadingChange,
  storageKey,
  exportType,
  exportTrigger,
  exportTitle = '無題',
  markKey,
  isSummary = false,
}) => {
  const [token, setToken] = useState('')
  const tableauRef = useRef<any>(null)
  const [uniqueFilters, setUniqueFilters] = useState<any[] | null>(null)
  const user = useSelector((state: RootState) => state.auth.user)
  const [markData, setMarkData] = useState<Record<string, any[]> | null>(null)
  const [data, setData] = useState<any[] | null>(null)
  const applyingFiltersRef = useRef(false)
  const previousFiltersRef = useRef<Record<string, any> | null>(null)
  let unionInfo = null

  if (user?.hierarchyLevel === 1) {
    const union = localStorage.getItem('union_selected_info')
    if (union) {
      try {
        unionInfo = JSON.parse(union).value
      } catch {
        localStorage.removeItem('union_selected_info')
      }
    }
  }

  useEffect(() => {
    if (data) {
      dataSheet?.(data)
      if (isData && storageKey) {
        localStorage.setItem(storageKey, JSON.stringify(data))
      }
    }
  }, [data, dataSheet, isData, storageKey])

  useEffect(() => {
    const loadApi = async () => {
      await loadTableauApi()
      try {
        const jwtToken = await fetchJwt()
        setToken(jwtToken)
      } catch (error) {
        console.error('Failed to fetch JWT token:', error)
      }
    }
    loadApi()
  }, [])

  const isInitialLoad = useRef(true)
  const [tableauApi, setTableauApi] = useState<TableauAPI | null>(null)
  const propsRef = useRef({
    onFiltersChange,
    dataSheet,
    isData,
    filters,
    onLoadingChange,
    storageKey,
    exportType,
  })
  useEffect(() => {
    propsRef.current = {
      onFiltersChange,
      dataSheet,
      isData,
      filters,
      onLoadingChange,
      storageKey,
      exportType,
    }
  }, [
    onFiltersChange,
    dataSheet,
    isData,
    filters,
    onLoadingChange,
    storageKey,
    exportType,
  ])

  useEffect(() => {
    if (!tableauApi) {
      loadTableauApi()
        .then((api) => {
          setTableauApi(api)
        })
        .catch(() => {})
    }
  }, [tableauApi])

  useEffect(() => {
    if (!token || !tableauRef.current || !tableauApi) return
    localStorage.setItem(TABLEAU_STORAGE_KEYS.IS_LOADING_TABLEAU, 'true')

    const vizElement = tableauRef.current

    const onFirstInteractive = async () => {
      try {
        const workbook = vizElement.workbook
        const activeSheet = workbook.activeSheet
        const targetSheet = findWorkSheet(workbook, activeSheet, 'Graph')

        // Load summary data if isData is true
        isData &&
          loadSummaryData(targetSheet, setData, propsRef.current.storageKey)

        const { FilterType } = tableauApi
        const [filters, parameters] = await Promise.all([
          targetSheet.getFiltersAsync().catch(() => []),
          workbook.getParametersAsync().catch(() => [] as any[]),
        ])

        await handleParseFilters(
          filters,
          FilterType,
          propsRef.current.onFiltersChange,
          parameters,
        )
        isInitialLoad.current = false
        localStorage.setItem(TABLEAU_STORAGE_KEYS.IS_LOADING_TABLEAU, 'false')
      } catch (error) {
        console.error('Error in onFirstInteractive:', error)
        localStorage.setItem(TABLEAU_STORAGE_KEYS.IS_LOADING_TABLEAU, 'false')
      }
    }
    vizElement.addEventListener(
      tableauApi.TableauEventType.FirstInteractive,
      onFirstInteractive,
    )

    const handleMarkSelectionChanged = async (event: any) => {
      if (!markKey) return

      const marks = await event.detail.getMarksAsync()
      const markInfo: Record<string, any[]> = {}
      markKey.forEach((key: string) => {
        const keyIndex = marks.data[0].columns.findIndex(
          (column: { fieldName?: string }) => column.fieldName?.includes(key),
        )
        markInfo[key] = Array.from(
          new Set(marks.data[0].data.map((row: any) => row[keyIndex].value)),
        )
      })
      setMarkData(markInfo)
    }

    vizElement.addEventListener(
      tableauApi.TableauEventType.MarkSelectionChanged,
      handleMarkSelectionChanged,
    )

    return () => {
      vizElement.removeEventListener(
        tableauApi.TableauEventType.FirstInteractive,
        onFirstInteractive,
      )
      vizElement.removeEventListener(
        tableauApi.TableauEventType.MarkSelectionChanged,
        handleMarkSelectionChanged,
      )
    }
  }, [token, tableauApi])

  const handleParseFilters = async (
    filters: any[],
    FilterType: any,
    onFiltersChange?: (filters: any[]) => void,
    parameters?: any[],
  ) => {
    await parseFilters(
      filters,
      FilterType,
      setUniqueFilters,
      onFiltersChange,
      parameters,
    )
  }

  const handleApplyParametersAndRegularFilters = async (
    workbook: any,
    activeSheet: any,
    filters: any[],
    filterValues: Record<string, any>,
    changedKeys?: string[],
  ) => {
    applyingFiltersRef.current = true
    await applyParametersAndRegularFilters(
      workbook,
      activeSheet,
      filters,
      filterValues,
      FilterUpdateType,
      changedKeys,
      propsRef.current.onFiltersChange,
    )
    setTimeout(() => {
      applyingFiltersRef.current = false
    }, 500)
  }

  const debouncedApplyFilters = useMemo(
    () =>
      debounce(async () => {
        const currentElement = tableauRef.current
        if (!currentElement?.workbook || isInitialLoad.current) return
        const currentFilters = uniqueFilters || []

        try {
          const workbook = currentElement.workbook
          const activeSheet = workbook.activeSheet

          if (propsRef.current.filters && currentFilters.length) {
            const currentFilterValues = propsRef.current.filters
            const previousFilterValues = previousFiltersRef.current
            let changedKeys: string[] | undefined

            if (previousFilterValues) {
              changedKeys = Object.keys(currentFilterValues).filter((key) => {
                const currentValue = currentFilterValues[key]
                const previousValue = previousFilterValues[key]

                if (
                  Array.isArray(currentValue) &&
                  Array.isArray(previousValue)
                ) {
                  return (
                    JSON.stringify(currentValue) !==
                    JSON.stringify(previousValue)
                  )
                }
                return currentValue !== previousValue
              })

              if (!changedKeys.length) {
                return
              }
            } else {
              changedKeys = undefined
            }

            await handleApplyParametersAndRegularFilters(
              workbook,
              activeSheet,
              currentFilters,
              currentFilterValues,
              changedKeys,
            )

            previousFiltersRef.current = { ...currentFilterValues }
          }
        } catch (error) {
          console.error('Error applying filters:', error)
        }
      }, 500),
    [uniqueFilters, filters],
  )

  useEffect(() => {
    if (uniqueFilters && !isInitialLoad.current) {
      debouncedApplyFilters()
    }
  }, [uniqueFilters, debouncedApplyFilters])

  useEffect(() => {
    if (filters && !previousFiltersRef.current) {
      previousFiltersRef.current = { ...filters }
    }
  }, [filters])

  const getEmplrId = () => {
    if (user?.hierarchyLevel !== 1) {
      return user?.emplrId
    }

    const unionInfoString = localStorage.getItem('union_selected_info')
    if (unionInfoString) {
      const unionInfo = JSON.parse(unionInfoString)
      return unionInfo?.value?.emplrId || user?.emplrId
    }

    return user?.emplrId
  }

  const triggerDownload = (blob: Blob, filename: string) => {
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', filename)
    document.body.appendChild(link)
    link.click()
    link.parentNode?.removeChild(link)
    window.URL.revokeObjectURL(url)
  }

  const handleExportPDF = async () => {
    if (!tableauRef.current?.workbook || !user) {
      console.error('Tableau workbook or user not available.')
      return
    }

    try {
      const viewName = tableauRef.current.workbook.activeSheet?.name
      if (!viewName) {
        console.error('Active sheet name not found.')
        return
      }

      const emplrId = getEmplrId()
      const formattedDate = getFormattedDate()

      const blob = await downloadPdf({
        viewId: viewName,
        filters: {
          ...filters,
          emplrId: emplrId,
        },
      })

      triggerDownload(blob, `${exportTitle}_${formattedDate}.pdf`)

      // Hide title after export
      tableauRef.current.workbook.changeParameterValueAsync(
        FILTER.showTitle,
        'false',
      )
    } catch (error) {
      console.error('Error exporting PDF:', error)
      return
    }
  }

  const handleExportCSV = async () => {
    if (!tableauRef.current?.workbook || !user) {
      console.error('Tableau workbook or user not available.')
      return
    }

    try {
      const viewName = tableauRef.current.workbook.activeSheet?.name
      if (!viewName) {
        console.error('Active sheet name not found.')
        return
      }

      const emplrId = getEmplrId()
      const formattedDate = getFormattedDate()

      const blob = await downloadCsv({
        viewId: viewName,
        filters: {
          ...filters,
          emplrId: emplrId,
        },
      })

      triggerDownload(blob, `${exportTitle}_表データ_${formattedDate}.csv`)
    } catch (error) {
      console.error('Error exporting CSV:', error)
      return
    }
  }

  const formatArrayValue = (value?: any[]) => {
    if (value && value.length) {
      if (value.indexOf('%all%') !== -1) {
        return 'すべて'
      }
      return `"${value}"`
    } else {
      return 'すべて'
    }
  }

  const processYearParameter = (
    value: any,
    isFiveYearPeriod: boolean,
  ): Record<string, number[]> => {
    const currentYear = parseInt(value, 10)
    if (isFiveYearPeriod) {
      return {
        fiscal_year: [
          currentYear - 4,
          currentYear - 3,
          currentYear - 2,
          currentYear - 1,
          currentYear,
        ],
      }
    }
    return { fiscal_year: [currentYear] }
  }

  const processAgeGroupParameter = (value: any): Record<string, any> => {
    if (!value?.length) {
      return {}
    }
    if (value[0].includes('-') || value[0].includes('歳以上')) {
      return { age_groups_increments_5: value }
    }
    return { age_groups_increments_10: value }
  }

  const prepareFilterBID = async (
    rawParams: Record<string, any>,
    filterType: string,
    isFiveYearPeriod: boolean,
  ): Promise<Record<string, any>> => {
    let result: Record<string, any> = {}
    for (const [key, value] of Object.entries(rawParams)) {
      switch (key) {
        case FILTER.yearParameter:
          result = {
            ...result,
            ...processYearParameter(value, isFiveYearPeriod),
          }
          break

        case FILTER.insurance:
          result = {
            ...result,
            gender_family_kbn: value
              .filter((v: string) => !v.includes('%all%'))
              .map((v: string) => v.replace(/\s+/g, '')),
          }
          break

        case FILTER.fiscalYear: {
          const listYear = value.map((v: string) => parseInt(v))
          result = { ...result, fiscal_year: listYear }
          break
        }

        case FILTER.member:
          result = { ...result, aggregation_cd: [parseInt(value) || 1] }
          break

        case FILTER.modal:
          result = { ...result, filter_type: [parseInt(filterType)] }
          break

        case FILTER.gender:
          if (!result.gender_family_kbn?.length) {
            const genderFamilyFilter = uniqueFilters?.find(
              (f: any) => f?.fieldName === FILTER.insurance,
            )
            if (genderFamilyFilter) {
              const genderFamilyDM = await genderFamilyFilter.getDomainAsync()
              const gender_family_kbn =
                genderFamilyDM?.values?.flatMap((item: { value: string }) =>
                  value.some((v: string) => item.value.includes(v)) ?
                    [item.value.replace(/\s+/g, '')]
                  : [],
                ) || []
              result = { ...result, gender_family_kbn }
            }
          } else {
            const gender_family_kbn = result.gender_family_kbn.filter(
              (item: string) => value.some((v: string) => item.includes(v)),
            )
            result = { ...result, gender_family_kbn }
          }
          break

        default:
          result = { ...result, ...processAgeGroupParameter(value) }
          break
      }
    }

    return result
  }

  const handleExportBID = async () => {
    if (!tableauRef.current?.workbook || !filterKey) {
      return
    }

    const currentFilterValues = filterKey.reduce(
      (acc, key) => {
        const isYearParam = key === FILTER.yearParameter
        const filter = uniqueFilters?.find((f) =>
          isYearParam ? f.name === key : f.fieldName.includes(key),
        )
        if (filter) {
          acc[key] =
            isYearParam ? filter.currentValue?.value : filter.appliedValues
        }
        return acc
      },
      {} as Record<string, any>,
    )

    const allFilters = Object.keys(currentFilterValues).reduce(
      (acc, key) => {
        acc[key] =
          propsRef.current.filters?.[key] ?
            propsRef.current.filters[key]
          : currentFilterValues[key]
        return acc
      },
      {} as Record<string, any>,
    )

    const csvContent: string[] = [
      `グラフタイトル,${exportTitle}`,
      '\n',
      '出力条件',
    ]

    const filterType: string =
      propsRef.current.filters?.[FILTER.filterType]?.[0] || '5'
    let isFiveYearPeriod: boolean = false

    Object.entries(allFilters).forEach(([key, value]) => {
      let label = key
      let formattedValue: string
      if (key === FILTER.yearParameter) {
        label = '期間'
        isFiveYearPeriod = !!uniqueFilters?.some((f) =>
          f.fieldName?.includes('5 years'),
        )
        if (isFiveYearPeriod && value) {
          const currentYear = parseInt(value, 10)
          formattedValue = `${currentYear - 4}年 - ${currentYear}年`
        } else {
          formattedValue = `${value}年`
        }
      } else if (key === FILTER.modal) {
        label = '記号・所属'
        formattedValue = filterType === '5' ? '記号' : `所属${filterType}`
      } else if (key === FILTER.member) {
        formattedValue = getMemberLabel(value)
      } else {
        if (key.includes('Age Group')) {
          label = '年齢階層'
        }
        formattedValue = formatArrayValue(value)
      }
      csvContent.push(`${label}, ${formattedValue}`)
    })

    if (markData && Object.values(markData).every((v) => v.length)) {
      Object.entries(markData).forEach(([key, value]) => {
        csvContent.push(`${key}, ${formatArrayValue(value)}`)
      })
    }

    const rawParams = { ...allFilters, ...markData }
    let filterParams = await prepareFilterBID(
      rawParams,
      filterType,
      isFiveYearPeriod,
    )

    filterParams = {
      ...filterParams,
      emplr_id: [unionInfo?.emplrId || user?.emplrId],
    }

    if (user?.hierarchyLevel === 3) {
      filterParams = {
        ...filterParams,
        jigyosyo_cd: [user.jigyosyoCd],
      }
    }

    csvContent.push('\n', '対象者')

    const listBID = await getBID(filterParams)

    if (listBID) {
      listBID.forEach((bid: string) => {
        csvContent.push(bid)
      })
    } else {
      csvContent.push('対象者なし')
    }

    const blob = new Blob([csvContent.join('\n')], {
      type: 'text/csv;charset=utf-8;',
    })
    const url = URL.createObjectURL(blob)
    const filename = `${exportTitle}_対象リスト_${new Date()
      .toISOString()
      .slice(0, 10)
      .replace(/-/g, '')}.csv`
    const a = document.createElement('a')
    a.href = url
    a.download = filename
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  useEffect(() => {
    if (!exportType) return

    switch (exportType) {
      case EXPORT_TYPES.PDF:
        handleExportPDF()
        break
      case EXPORT_TYPES.CSV:
        handleExportCSV()
        break
      case EXPORT_TYPES.BID:
        handleExportBID()
        break
      default:
        break
    }
  }, [exportType, exportTrigger])

  if (!token) return <>Loading Tableau...</>

  return src ?
      <div
        className={`flex justify-center overflow-hidden ${isSummary ? '' : 'mt-10'}`}
      >
        {/* @ts-ignore */}
        <tableau-viz
          ref={tableauRef}
          id={'tableauViz'}
          src={src}
          token={token}
          toolbar={'hidden'}
          hide-tabs={true}
          style={{
            transition: 'opacity 0.3s ease-in-out',
          }}
        >
          {/* @ts-ignore */}
          <viz-filter
            field={FILTER.emplrId}
            value={unionInfo?.emplrId || user?.emplrId}
          />
          {user?.hierarchyLevel === 3 && (
            // @ts-ignore
            <viz-filter field={FILTER.jigyosyoCd} value={user?.jigyosyoCd} />
          )}
          {/* @ts-ignore */}
        </tableau-viz>
      </div>
    : <div
        className={
          'mt-4 flex h-60 items-center justify-center bg-green-100 text-center text-4xl text-green-400'
        }
      >
        tableauで作成した表が入ります
      </div>
}

export default TableauWeb
