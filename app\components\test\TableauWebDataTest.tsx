import React, { useState, useEffect } from 'react'
import TableauWeb from '../tableau/tableauWeb'
import { TABLEAU_LINKS, TABLEAU_STORAGE_KEYS } from '../../constants/tableauLinks'

const TableauWebDataTest: React.FC = () => {
  const [dataSheet, setDataSheet] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(false)

  const handleData = (data: any[]) => {
    console.log('📊 Dữ liệu nhận được từ TableauWeb:', data)
    setDataSheet(data)
  }

  const handleLoadingChange = (loading: boolean) => {
    setIsLoading(loading)
    console.log('🔄 Loading state:', loading)
  }

  useEffect(() => {
    const fetchDataFromLocalStorage = () => {
      const cachedData = localStorage.getItem(TABLEAU_STORAGE_KEYS.RECEIPT_DATA)
      if (cachedData) {
        try {
          const parsedData = JSON.parse(cachedData)
          setDataSheet(parsedData)
          console.log('💾 Dữ liệu từ localStorage:', parsedData)
        } catch (error) {
          console.error('❌ Lỗi parse localStorage:', error)
        }
      }
    }
    fetchDataFromLocalStorage()
  }, [])

  return (
    <div style={{ padding: '20px', border: '2px solid #ccc', margin: '20px' }}>
      <h3>🧪 Test TableauWeb Data Only Feature</h3>
      
      <div style={{ marginBottom: '20px' }}>
        <p><strong>Loading:</strong> {isLoading ? '⏳ Đang tải...' : '✅ Hoàn thành'}</p>
        <p><strong>Số dòng dữ liệu:</strong> {dataSheet.length}</p>
      </div>

      {dataSheet.length > 0 && (
        <div style={{ marginBottom: '20px' }}>
          <h4>📋 Dữ liệu mẫu (5 dòng đầu):</h4>
          <pre style={{ 
            background: '#f5f5f5', 
            padding: '10px', 
            borderRadius: '4px',
            fontSize: '12px',
            overflow: 'auto',
            maxHeight: '200px'
          }}>
            {JSON.stringify(dataSheet.slice(0, 5), null, 2)}
          </pre>
        </div>
      )}

      {/* TableauWeb ẩn để chỉ lấy dữ liệu */}
      <div style={{ 
        opacity: 0, 
        width: 0, 
        height: 0, 
        overflow: 'hidden',
        position: 'absolute',
        left: '-9999px'
      }}>
        <TableauWeb
          src={TABLEAU_LINKS.NUMBER_SUMMARY}
          dataSheet={handleData}
          isData={true}
          storageKey={TABLEAU_STORAGE_KEYS.RECEIPT_DATA}
          onLoadingChange={handleLoadingChange}
        />
      </div>
    </div>
  )
}

export default TableauWebDataTest
