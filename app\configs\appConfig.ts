type AppConfig = {
  apiBaseUrl: string
  apiTimeout: number
  environment: string
  maintenanceMode: boolean
  auth0Domain: string
  auth0ClientId: string
  apiTableauUrl: string
}

export const AppConfig: AppConfig = {
  environment: import.meta.env.VITE_ENVIRONMENT,
  apiBaseUrl: import.meta.env.VITE_API_BASE_URL,
  apiTableauUrl: import.meta.env.VITE_API_TABLEAU_URL,
  apiTimeout: import.meta.env.VITE_API_TIMEOUT,
  maintenanceMode: import.meta.env.VITE_MAINTENANCE_MODE === 'true',
  auth0Domain: import.meta.env.VITE_AUTH0_DOMAIN,
  auth0ClientId: import.meta.env.VITE_AUTH0_CLIENT_ID,
}

export const routeUrl = {
  LOGIN: '/login',
  PROFILE: '/profile',
  RECEIPT: {
    path: '/receipt',
    children: {
      MEDICAL_EXPENSE: {
        path: 'medical-expense',
        children: {
          CLASSIFICATION: 'classification',
          COUNT: 'count',
          ANNUAL_RANGE: 'annual-range',
          AGE_GROUP: 'age-group',
          COST_ANALYSIS: 'cost-analysis',
          DISEASE_CLASSIFICATION: 'disease-classification',
          SPECIFIC_DISEASE_RATIO: 'specific-disease-ratio',
        },
      },
      LIFESTYLE_DISEASE: {
        path: 'lifestyle-disease',
        children: {
          EXPENSE_TREND: 'expense-trend',
          PATIENT_COUNT: 'patient-count',
          AGE_GROUP_RATIO: 'age-group-ratio',
        },
      },
      CANCER: {
        path: 'cancer',
        children: {
          EXPENSE_TREND: 'expense-trend',
          PATIENT_COUNT: 'patient-count',
          AGE_GROUP_RATIO: 'age-group-ratio',
        },
      },
      DENTAL: {
        path: 'dental',
        children: {
          EXPENSE_TREND: 'expense-trend',
          PATIENT_COUNT: 'patient-count',
          AGE_GROUP_RATIO: 'age-group-ratio',
        },
      },
      EXAMINATION_STATUS: {
        path: 'examination-status',
      },
    },
  },
  HEALTH_CHECK_FORM: {
    path: '/health-check-form',
    children: {
      CHECKUP_RATE: {
        path: 'checkup-rate',
      },
      ITEM_DETAILS: {
        path: 'item-details',
        children: {
          HEALTH_CHECK: 'health-check',
          QUESTIONNAIRE: 'questionnaire',
          HEALTH_CHECK_RISK: 'health-check-risk',
          QUESTIONNAIRE_RISK: 'questionnaire-risk',
        },
      },
      OUT_OF_STANDARD: {
        path: 'out-of-standard',
        children: {
          HEALTH_CHECK_RATE: 'health-check-rate',
          QUESTIONNAIRE_RATE: 'questionnaire-rate',
        },
      },
      METABOLIC_SYNDROME: {
        path: 'metabolic-syndrome',
        children: {
          EXPENSE_TREND: 'expense-trend',
          PATIENT_COUNT: 'patient-count',
          AGE_GROUP_RATIO: 'age-group-ratio',
        },
      },
      HEALTH_ISSUE_MAP: {
        path: 'health-issue-map',
        children: {
          EXPENSE_TREND: 'expense-trend',
          PATIENT_COUNT: 'patient-count',
          AGE_GROUP_RATIO: 'age-group-ratio',
        },
      },
    },
  },
  MEMBER_INFO: {
    path: '/member-info',
    children: {
      MEMBER_COUNT: {
        path: 'member-count',
      },
      MEMBER_COMPOSITION: {
        path: 'member-composition',
        children: {
          ATTRIBUTE_COMPOSITION: 'attribute-composition',
          AGE_GROUP_COMPOSITION: 'age-group-composition',
          GENDER_COMPOSITION: 'gender-composition',
        },
      },
      AVERAGE_AGE: {
        path: 'average-age',
      },
    },
  },
  HEALTH_CARE_MANAGEMENT: {
    path: '/health-care-management',
    children: {
      SPECIFIC_HEALTH_CHECK: {
        path: 'specific-health-check',
        children: {
          SPECIFIC_HEALTH_CHECK_RATE: 'specific-health-check-rate',
          TARGET_POPULATION_RATE: 'target-population-rate',
          OBESITY_REDUCTION_RATE: 'obesity-reduction-rate',
        },
      },
      RECOMMENDED_HEALTH_CHECK: {
        path: 'recommended-health-check',
        children: {
          LIFESTYLE_DISEASE_TREATMENT_HOLDERS:
            'lifestyle-disease-treatment-holders',
          DIABETES_UNCONTROLLED_PATIENTS: 'diabetes-uncontrolled-patients',
        },
      },
      CANCER: {
        path: 'cancer',
        children: {
          CANCER_EXAMINATION: 'cancer-examination',
        },
      },
      DIABETES: {
        path: 'diabetes',
        children: {
          KIDNEY_RISK_COUNT: 'kidney-risk-count',
          ANNUAL_DIALYSIS_TREND: 'annual-dialysis-trend',
        },
      },
    },
  },
  HOME: '/',
  NOT_FOUND: '/not-found',
  NOTICE: {
    path: '/notices',
  },
  REGULAR_REPORT: {
    path: '/regular-report',
  },
  DOWNLOAD_LIST: {
    path: '/download-list',
  },
}

export const pathsHasFooter = [
  routeUrl.HOME,
  routeUrl.LOGIN,
  routeUrl.NOT_FOUND,
]
export const pathsNotHasSidebar = [routeUrl.NOT_FOUND]
