import title from './title'

export const MEDICAL_EXPENSE_TAB = [
  {
    label: title.receiptInformation.medicalExpense.child.receiptClassification,
    key: 'RECEIPT_CLASSIFICATION',
    route: '/receipt/medical-expense/classification',
  },
  {
    label: title.receiptInformation.medicalExpense.child.receiptCount,
    key: 'RECEIPT_COUNT',
    route: '/receipt/medical-expense/count',
  },
  {
    label: title.receiptInformation.medicalExpense.child.annualRange,
    key: 'ANNUAL_RANGE',
    route: '/receipt/medical-expense/annual-range',
  },
  {
    label: title.receiptInformation.medicalExpense.child.ageGroup,
    key: 'AGE_GROUP',
    route: '/receipt/medical-expense/age-group',
  },
  {
    label: title.receiptInformation.medicalExpense.child.costAnalysis,
    key: 'COST_ANALYSIS',
    route: '/receipt/medical-expense/cost-analysis',
  },
  {
    label: title.receiptInformation.medicalExpense.child.diseaseClassification,
    key: 'DISEASE_CLASSIFICATION',
    route: '/receipt/medical-expense/disease-classification',
  },
  {
    label: title.receiptInformation.medicalExpense.child.specificDiseaseRatio,
    key: 'SPECIFIC_DISEASE_RATIO',
    route: '/receipt/medical-expense/specific-disease-ratio',
  },
]

export const EXAMINATION_STATUS_TAB = [
  {
    label: title.receiptInformation.examinationStatus.child.hospitalVisitStatus,
    key: 'HOSPITAL_VISIT_STATUS',
    route: '/receipt/examination-status/hospital-visit-status',
  },
]

export const PERIOD = {
  MONTH: 'month',
  YEAR: 'year',
}
export const EXPORT_TYPES = {
  PDF: 'pdf',
  CSV: 'csv',
  BID: 'bid',
}

export const PDF_EXPORT_OPTIONS = {
  THIS_VIEW: 'this_view',
  ALL_VIEWS: 'all_views',
}

export const PDF_SCALING_OPTIONS = {
  AUTOMATIC: 'automatic',
  PERC_25: '25%',
  PERC_50: '50%',
  PERC_60: '60%',
  PERC_75: '75%',
  PERC_80: '80%',
  PERC_90: '90%',
  PERC_100: '100%',
  PERC_200: '200%',
  PERC_400: '400%',
}

export const PDF_PAGE_SIZE_OPTIONS = {
  LETTER: 'letter',
  LEGAL: 'legal',
  TABLOID: 'tabloid',
  NOTE: 'note',
  FOLIO: 'folio',
  A3: 'A3',
  A4: 'A4',
  A5: 'A5',
  B4: 'B4',
  B5: 'B5',
}

export const PDF_ORIENTATION_OPTIONS = {
  PORTRAIT: 'portrait',
  LANDSCAPE: 'landscape',
}

export const LIFESTYLE_DISEASE_TAB = [
  {
    label: title.receiptInformation.lifestyleDisease.child.expenseTrend,
    key: 'LIFESTYLE_DISEASE_EXPENSE_TREND',
    route: '/receipt/lifestyle-disease/expense-trend',
  },
  {
    label: title.receiptInformation.lifestyleDisease.child.patientCount,
    key: 'LIFESTYLE_DISEASE_PATIENT_COUNT',
    route: '/receipt/lifestyle-disease/patient-count',
  },
  {
    label: title.receiptInformation.lifestyleDisease.child.ageGroupRatio,
    key: 'LIFESTYLE_DISEASE_AGE_GROUP_RATIO',
    route: '/receipt/lifestyle-disease/age-group-ratio',
  },
]

export const CANCER_TAB = [
  {
    label: title.receiptInformation.cancer.child.expenseTrend,
    key: 'CANCER_EXPENSE_TREND',
    route: '/receipt/cancer/expense-trend',
  },
  {
    label: title.receiptInformation.cancer.child.patientCount,
    key: 'CANCER_PATIENT_COUNT',
    route: '/receipt/cancer/patient-count',
  },
  {
    label: title.receiptInformation.cancer.child.ageGroupRatio,
    key: 'CANCER_AGE_GROUP_RATIO',
    route: '/receipt/cancer/age-group-ratio',
  },
]

export const DENTAL_TAB = [
  {
    label: title.receiptInformation.dental.child.expenseTrend,
    key: 'DENTAL_EXPENSE_TREND',
    route: '/receipt/dental/expense-trend',
  },
  {
    label: title.receiptInformation.dental.child.patientCount,
    key: 'DENTAL_PATIENT_COUNT',
    route: '/receipt/dental/patient-count',
  },
  {
    label: title.receiptInformation.dental.child.ageGroupRatio,
    key: 'DENTAL_AGE_GROUP_RATIO',
    route: '/receipt/dental/age-group-ratio',
  },
]

export const CHECKUP_RATE_TAB = [
  {
    label: title.healthCheckForm.checkupRate.child.healthCheckRate,
    key: 'HEALTH_CHECK_RATE',
    route: '/health-check-form/health-check-rate',
  },
]

export const ITEM_DETAILS_TAB = [
  {
    label: title.healthCheckForm.itemDetails.child.healthCheck,
    key: 'HEALTH_CHECK',
    route: '/health-check-form/item-details/health-check',
  },
  {
    label: title.healthCheckForm.itemDetails.child.questionnaire,
    key: 'QUESTIONNAIRE',
    route: '/health-check-form/item-details/questionnaire',
  },
  {
    label: title.healthCheckForm.itemDetails.child.healthCheckRisk,
    key: 'HEALTH_CHECK_RISK',
    route: '/health-check-form/item-details/health-check-risk',
  },
  {
    label: title.healthCheckForm.itemDetails.child.questionnaireRisk,
    key: 'QUESTIONNAIRE_RISK',
    route: '/health-check-form/item-details/questionnaire-risk',
  },
]

export const OUT_OF_STANDARD_TAB = [
  {
    label: title.healthCheckForm.outOfStandard.child.healthCheckRate,
    key: 'HEALTH_CHECK_RATE',
    route: '/health-check-form/out-of-standard/health-check-rate',
  },
  {
    label: title.healthCheckForm.outOfStandard.child.questionnaireRate,
    key: 'QUESTIONNAIRE_RATE',
    route: '/health-check-form/out-of-standard/questionnaire-rate',
  },
]

export const METABOLIC_SYNDROME_TAB = [
  {
    label: title.healthCheckForm.metabolicSyndrome.child.expenseTrend,
    key: 'EXPENSE_TREND',
    route: '/health-check-form/metabolic-syndrome/expense-trend',
  },
]

export const HEALTH_ISSUE_MAP_TAB = [
  {
    label: title.healthCheckForm.healthIssueMap.title,
    key: 'HEALTH_ISSUE_MAP',
    route: '/health-check-form/health-issue-map',
  },
]

export const MEMBER_COUNT_TAB = [
  {
    label: title.memberInformation.memberCount.child.subscriberNumber,
    key: 'MEMBER_COUNT',
    route: '/member-info/member-count',
  },
]

export const MEMBER_COMPOSITION_TAB = [
  {
    label: title.memberInformation.memberComposition.child.attributeComposition,
    key: 'ATTRIBUTE_COMPOSITION',
    route: '/member-info/member-composition/attribute-composition',
  },
  {
    label: title.memberInformation.memberComposition.child.ageGroupComposition,
    key: 'AGE_GROUP_COMPOSITION',
    route: '/member-info/member-composition/age-group-composition',
  },
  {
    label: title.memberInformation.memberComposition.child.genderComposition,
    key: 'GENDER_COMPOSITION',
    route: '/member-info/member-composition/gender-composition',
  },
]

export const AVERAGE_AGE_TAB = [
  {
    label: title.memberInformation.averageAge.child.averageAgeChange,
    key: 'AVERAGE_AGE',
    route: '/member-info/average-age',
  },
]

export const SPECIFIC_HEALTH_CHECK_TAB = [
  {
    label:
      title.healthCareManagement.specificHealthCheck.child
        .specificHealthCheckRate,
    key: 'SPECIFIC_HEALTH_CHECK_RATE',
    route:
      '/health-care-management/specific-health-check/specific-health-check-rate',
  },
  {
    label:
      title.healthCareManagement.specificHealthCheck.child.targetPopulationRate,
    key: 'TARGET_POPULATION_RATE',
    route:
      '/health-care-management/specific-health-check/target-population-rate',
  },
  {
    label:
      title.healthCareManagement.specificHealthCheck.child.obesityReductionRate,
    key: 'OBESITY_REDUCTION_RATE',
    route:
      '/health-care-management/specific-health-check/obesity-reduction-rate',
  },
]

export const RECOMMENDED_HEALTH_CHECK_TAB = [
  {
    label:
      title.healthCareManagement.recommendedHealthCheck.child
        .lifestyleDiseaseTreatmentHolders,
    key: 'LIFESTYLE_DISEASE_TREATMENT_HOLDERS',
    route:
      '/health-care-management/recommended-health-check/lifestyle-disease-treatment-holders',
  },
  {
    label:
      title.healthCareManagement.recommendedHealthCheck.child
        .diabetesUncontrolledPatients,
    key: 'DIABETES_UNCONTROLLED_PATIENTS',
    route:
      '/health-care-management/recommended-health-check/diabetes-uncontrolled-patients',
  },
]

export const HEALTH_CARE_MANAGEMENT_CANCER_TAB = [
  {
    label: title.healthCareManagement.cancer.child.cancelExamination,
    key: 'CANCER_EXAMINATION',
    route: '/health-care-management/cancer/cancer-examination',
  },
]

export const DIABETES_TAB = [
  {
    label: title.healthCareManagement.diabetes.child.kidneyRiskCount,
    key: 'KIDNEY_RISK_COUNT',
    route: '/health-care-management/diabetes/kidney-risk-count',
  },
  {
    label: title.healthCareManagement.diabetes.child.annualDialysisTrend,
    key: 'ANNUAL_DIALYSIS_TREND',
    route: '/health-care-management/diabetes/annual-dialysis-trend',
  },
]

export const NUMBER_DIVIDER_AGENT = 13

export const DOWNLOAD_LIST_TABLE = {
  error: 'エラー',
  done: '完了',
  creating: '作成中',
}
