import type { JSX } from 'react'
import {
  DeveloperGuideIcon,
  Download,
  EventNoteIcon,
  GroupSearch,
  HomeIcon,
  MenuList,
  QuickReference,
  ReadinessScore,
  StethoscopeSearch,
  SupportAgent,
} from '../components/common/Icon'
import { routeUrl } from '../configs/appConfig'
import title from './title'

export type ROUTER = {
  pathname: string
  hasLayout: boolean
  label: string
  children?: ROUTER[]
  hasDivider?: boolean
  icon?: (props: {
    fill?: string
    width?: string
    height?: string
  }) => JSX.Element
  number?: number
}

export const SIDEBAR_ROUTER: { [key: string]: ROUTER } = {
  home: {
    pathname: '/',
    hasLayout: true,
    label: title.home.title,
    icon: (props) => <HomeIcon {...props} />,
    number: 1,
  },
  receipt: {
    pathname: '/receipt',
    hasLayout: true,
    label: title.receiptInformation.title,
    icon: (props) => <QuickReference {...props} />,
    children: [
      {
        pathname: `${routeUrl.RECEIPT.path}/${routeUrl.RECEIPT.children.MEDICAL_EXPENSE.path}`,
        hasLayout: true,
        label: title.receiptInformation.medicalExpense.title,
      },
      {
        pathname: `${routeUrl.RECEIPT.path}/${routeUrl.RECEIPT.children.EXAMINATION_STATUS.path}`,
        hasLayout: true,
        label: title.receiptInformation.examinationStatus.title,
        hasDivider: true,
      },
      {
        pathname: `${routeUrl.RECEIPT.path}/${routeUrl.RECEIPT.children.LIFESTYLE_DISEASE.path}`,
        hasLayout: true,
        label: title.receiptInformation.lifestyleDisease.title,
      },
      {
        pathname: `${routeUrl.RECEIPT.path}/${routeUrl.RECEIPT.children.CANCER.path}`,
        hasLayout: true,
        label: title.receiptInformation.cancer.title,
      },
      {
        pathname: `${routeUrl.RECEIPT.path}/${routeUrl.RECEIPT.children.DENTAL.path}`,
        hasLayout: true,
        label: title.receiptInformation.dental.title,
      },
    ],
    number: 2,
  },
  healthCheckForm: {
    pathname: '/health-check-form',
    hasLayout: true,
    label: title.healthCheckForm.title,
    icon: (props) => <StethoscopeSearch {...props} />,
    children: [
      {
        pathname: `${routeUrl.HEALTH_CHECK_FORM.path}/${routeUrl.HEALTH_CHECK_FORM.children.CHECKUP_RATE.path}`,
        hasLayout: true,
        label: title.healthCheckForm.checkupRate.title,
      },
      {
        pathname: `${routeUrl.HEALTH_CHECK_FORM.path}/${routeUrl.HEALTH_CHECK_FORM.children.ITEM_DETAILS.path}`,
        hasLayout: true,
        label: title.healthCheckForm.itemDetails.title,
      },
      {
        pathname: `${routeUrl.HEALTH_CHECK_FORM.path}/${routeUrl.HEALTH_CHECK_FORM.children.OUT_OF_STANDARD.path}`,
        hasLayout: true,
        label: title.healthCheckForm.outOfStandard.title,
      },
      {
        pathname: `${routeUrl.HEALTH_CHECK_FORM.path}/${routeUrl.HEALTH_CHECK_FORM.children.METABOLIC_SYNDROME.path}`,
        hasLayout: true,
        label: title.healthCheckForm.metabolicSyndrome.title,
      },
      {
        pathname: `${routeUrl.HEALTH_CHECK_FORM.path}/${routeUrl.HEALTH_CHECK_FORM.children.HEALTH_ISSUE_MAP.path}`,
        hasLayout: true,
        label: title.healthCheckForm.healthIssueMap.title,
      },
    ],
    number: 3,
  },
  memberInfo: {
    pathname: '/member-info',
    hasLayout: true,
    label: title.memberInformation.title,
    icon: (props) => <GroupSearch {...props} />,
    hasDivider: true,
    children: [
      {
        pathname: `${routeUrl.MEMBER_INFO.path}/${routeUrl.MEMBER_INFO.children.MEMBER_COUNT.path}`,
        hasLayout: true,
        label: title.memberInformation.memberCount.title,
      },
      {
        pathname: `${routeUrl.MEMBER_INFO.path}/${routeUrl.MEMBER_INFO.children.MEMBER_COMPOSITION.path}`,
        hasLayout: true,
        label: title.memberInformation.memberComposition.title,
      },
      {
        pathname: `${routeUrl.MEMBER_INFO.path}/${routeUrl.MEMBER_INFO.children.AVERAGE_AGE.path}`,
        hasLayout: true,
        label: title.memberInformation.averageAge.title,
      },
    ],
    number: 4,
  },
  dividerInfo: {
    pathname: '',
    hasLayout: true,
    label: '',
    number: 5,
  },
  healthCareManagement: {
    pathname: '/health-care-management',
    hasLayout: true,
    label: title.healthCareManagement.title,
    icon: (props) => <ReadinessScore {...props} />,
    children: [
      {
        pathname: `${routeUrl.HEALTH_CARE_MANAGEMENT.path}/${routeUrl.HEALTH_CARE_MANAGEMENT.children.SPECIFIC_HEALTH_CHECK.path}`,
        hasLayout: true,
        label: title.healthCareManagement.specificHealthCheck.title,
      },
      {
        pathname: `${routeUrl.HEALTH_CARE_MANAGEMENT.path}/${routeUrl.HEALTH_CARE_MANAGEMENT.children.RECOMMENDED_HEALTH_CHECK.path}`,
        hasLayout: true,
        label: title.healthCareManagement.recommendedHealthCheck.title,
      },
      {
        pathname: `${routeUrl.HEALTH_CARE_MANAGEMENT.path}/${routeUrl.HEALTH_CARE_MANAGEMENT.children.CANCER.path}`,
        hasLayout: true,
        label: title.healthCareManagement.cancer.title,
      },
      {
        pathname: `${routeUrl.HEALTH_CARE_MANAGEMENT.path}/${routeUrl.HEALTH_CARE_MANAGEMENT.children.DIABETES.path}`,
        hasLayout: true,
        label: title.healthCareManagement.diabetes.title,
      },
    ],
    number: 6,
  },
  regularReport: {
    pathname: '/regular-report',
    hasLayout: true,
    label: title.regularReport.title,
    icon: (props) => <MenuList {...props} />,
    number: 7,
  },
  downloadList: {
    pathname: '/download-list',
    hasLayout: true,
    label: title.downloadList.title,
    icon: (props) => <Download {...props} />,
    hasDivider: true,
    number: 8,
  },
  dividerCalendar: {
    pathname: '',
    hasLayout: true,
    label: '',
    number: 9,
  },
  calendar: {
    pathname: '',
    hasLayout: true,
    label: title.dataExtractionPeriod.title,
    icon: (props) => <EventNoteIcon {...props} />,
    number: 10,
  },
  bookMarked: {
    pathname: '',
    hasLayout: true,
    label: title.manual.title,
    icon: (props) => <DeveloperGuideIcon {...props} />,
    number: 11,
  },
  supportAgentIcon: {
    pathname: '',
    hasLayout: true,
    label: title.contact.title,
    icon: (props) => <SupportAgent {...props} />,
    number: 12,
  },
  dividerAgent: {
    pathname: '',
    hasLayout: true,
    label: '',
    number: 13,
  },
}
