export default {
  root: 'らくらく健助',
  notFound: {
    title: 'イラスト未対応',
    description: 'お探しのページは見つかりませんでした',
    button: 'TOPへ戻る',
  },
  home: {
    title: 'ホーム',
    more: 'もっと見る',
    noticeDescription: 'このページはできるだけ早く更新し続けます。',
  },
  notice: {
    title: 'お知らせ',
    detail: '詳細',
    backToList: '一覧へ戻る',
  },
  header: {
    title: '健助プラス ロゴ',
    logout: 'ログアウト',
    accountName: 'アカウント名',
    unionTitle: '組合名または組合コードを入力してください',
    unionSubmit: '設定する',
  },
  classification: {
    all: '全体',
    individual: '加入者ー人あたり',
  },
  filterByYear: {
    year: '年度推移',
    month: '月推移',
  },
  transferListModal: {
    title: '絞り込むカテゴリーを選択してください。',
  },
  button: {
    export: '出力',
    cancel: 'キャンセル',
    submit: '決定する',
    login: 'ログイン',
  },
  login: {
    loginTitle: '健助＋',
    maintainTitle: 'メンテナンス情報',
    maintainDesc: 'ただいまメンテナンス中のため、ご利用いただけません。',
  },

  receiptInformation: {
    title: 'レセプト情報',
    description: '医療費・受診状況・疾病',
    description1:
      '医療費・受診状況・疾病の特徴などを表示します。高額の医療費や疾患ごとの医療費を確認し、組合の医療費削減の参考にしましょう。',
    description2: '集計数に関しては、前年度の同時期と比較しています。',
    receiptTotalAmount: '医療費総額推移',
    medicalTotalAmount: '医療費総額',
    perPersonMedicalExpense: '加入者一人あたり',
    actualPatientNumber: '実患者数',
    medicalInstitutionVisitRate: '医療機関受診率',
    medicalExpense: {
      title: '医療費',
      description: '医療費の特性・リスク分析',
      child: {
        receiptClassification: 'レセプト種別',
        receiptCount: 'レセプト件数',
        annualRange: '年間レンジ',
        ageGroup: '年齢階層別',
        costAnalysis: '高額医療費構成',
        diseaseClassification: '疾患分類ごと',
        specificDiseaseRatio: '特定分類疾患の割合',
      },
    },
    examinationStatus: {
      title: '受診状況',
      description: '受診状況の特性・リスク分析',
      child: {
        hospitalVisitStatus: '医療機関受診状況',
      },
    },
    lifestyleDisease: {
      title: '生活習慣病',
      description: '生活習慣病の特性・リスク分析',
      child: {
        expenseTrend: '医療費推移',
        patientCount: '患者数',
        ageGroupRatio: '年齢階層別割合',
      },
    },
    cancer: {
      title: 'がん',
      description: 'がんの特性・リスク分析',
      child: {
        expenseTrend: '医療費推移',
        patientCount: '患者数',
        ageGroupRatio: '年齢階層別割合',
      },
    },
    dental: {
      title: '歯科',
      description: '歯科の特性・リスク分析',
      child: {
        expenseTrend: '医療費推移',
        patientCount: '患者数',
        ageGroupRatio: '年齢階層別割合',
      },
    },
  },
  healthCheckForm: {
    title: '健康診断・問診情報',
    description: '健診・問診の特性・リスク分析',
    description1:
      '各種健診・問診の特徴・リスク者分析などの経年変化を確認することが可能です。健診受診状況を確認することで、加入者の健康管理に活用しましょう。',
    description2: '集計数に関しては、前年度の同時期と比較しています。',
    examineeTrend: '健診受診者数推移',
    examineeCount: '健診受診者数',
    metabolicSyndromeRate: 'メタボリックシンドローム該当率',
    metabolicSyndromeCount: 'メタボリックシンドローム該当者',
    examinationRate: '健診受診率',
    checkupRate: {
      title: '受診率',
      description: '健診受診率の推移を確認できます',
      child: {
        healthCheckRate: '健診受診率',
      },
    },
    itemDetails: {
      title: '項目別詳細',
      description: '健診項目ごとの詳細データを確認できます',
      child: {
        healthCheck: '健診項目一覧',
        questionnaire: '問診項目一覧',
        healthCheckRisk: '健診リスク者割合比較',
        questionnaireRisk: '問診リスク者割合比較',
      },
    },
    outOfStandard: {
      title: '基準値以上該当',
      description: '基準値を超える項目の分析結果を表示します',
      child: {
        healthCheckRate: '健診基準値以上割合',
        questionnaireRate: '問診基準値以上割合',
      },
    },
    metabolicSyndrome: {
      title: 'メタボ',
      title2: 'メタボリックシンドローム',
      description: 'メタボリックシンドロームに関する分析結果を確認できます',
      child: {
        expenseTrend: 'メタボリックシンドローム該当者割合推移',
      },
    },
    healthIssueMap: {
      title: '健康課題マップ',
      description: '健康に関する課題を視覚的に確認できます',
    },
  },
  memberInformation: {
    title: '加入者情報',
    description: '記号・年齢・男女別の加入者数',
    description1:
      '記号別、年齢階層別、男女別の組合の加入者情報を表示します。組合の年度ごとの変化を把握することができます。',
    description2:
      '集計数に関しては、前年度の同期間で一度でも加入した人が対象となります。',
    memberCountTrend: '加入者数推移',
    insuredMemberCount: '被保険者数',
    insuredMemberAverageAge: '被保険者平均年齢',
    memberCount: {
      title: '加入者数',
      description: '加入者数の特性・リスク分析など',
      child: {
        subscriberNumber: '年度別加入者数推移',
      },
    },
    memberComposition: {
      title: '加入者構成',
      description: '加入者構成の特性・リスク分析など',
      child: {
        attributeComposition: '属性構成図',
        ageGroupComposition: '年齢層構成',
        genderComposition: '性別構成',
      },
    },
    averageAge: {
      title: '平均年齢',
      description: '平均年齢の特性・リスク分析など',
      child: {
        averageAgeChange: '平均年齢推移',
      },
    },
  },
  healthCareManagement: {
    title: '保健事業管理',
    description: '総合評価指標・効果測定',
    description1:
      '保健事業の数字を確認し、加入者の健康増進施策を進めることによって、加入者の生活習慣病の予防をおこなっていきましょう。',
    specificHealthCheckRate: '特定健診 受診率',
    obesityReductionRate: '肥満解消率',
    specificHealthCheck: {
      title: '特定健診・保健指導',
      description: '特定健診・特定保健指導の特性・リスク分析など',
      child: {
        specificHealthCheckRate: '特定健診受診率',
        targetPopulationRate: '対象者割合・流出入推移',
        obesityReductionRate: '肥満解消率',
      },
    },
    recommendedHealthCheck: {
      title: '受診勧奨・重症化予防',
      description: '受診勧奨・重症化予防の特性・リスク分析など',
      child: {
        lifestyleDiseaseTreatmentHolders: '生活習慣病治療放置者数・中断者数',
        diabetesUncontrolledPatients: '糖尿病アンコントロール者推移',
      },
    },
    cancer: {
      title: 'がん',
      description: 'がんの特性・リスク分析など',
      child: {
        cancelExamination: '検診・要精密検査受診者',
      },
    },
    diabetes: {
      title: '糖尿病',
      description: '糖尿病の特性・リスク分析など',
      child: {
        kidneyRiskCount: '腎機能低下・人工透析リスク者数',
        annualDialysisTrend: '人工透析患者数年間推移',
      },
    },
  },
  regularReport: {
    title: '定期レポート',
    description: '医療費・健診・加入者のレポート',
  },
  downloadList: {
    title: 'ダウンロード一覧',
    description: '出力したデータの確認',
  },
  dataExtractionPeriod: {
    title: 'データ抽出期間',
    description: 'データ抽出期間の設定',
  },
  manual: {
    title: 'マニュアル',
    description: 'マニュアルの確認',
  },
  contact: {
    title: 'ヘルプ・FAQ',
    subTitle: 'お問い合わせ',
    description: 'チャットで回答',
  },
  variousSettings: {
    title: '各種設定',
    description: 'さまざまな設定の確認',
  },
  diseaseAnalysis: '疾病ごとに分析する',
  itemAnalysis: '項目ごとに分析する',
}
