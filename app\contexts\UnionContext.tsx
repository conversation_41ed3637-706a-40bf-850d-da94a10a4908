import type { ReactNode } from 'react'
import React, { createContext, useContext, useState } from 'react'

type UnionContextType = {
  unionRefreshKey: number
  refreshUnion: () => void
}

const UnionContext = createContext<UnionContextType | undefined>(undefined)

export const UnionProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const [unionRefreshKey, setUnionRefreshKey] = useState(0)

  const refreshUnion = () => {
    setUnionRefreshKey((prev) => prev + 1)
  }

  return (
    <UnionContext.Provider value={{ unionRefreshKey, refreshUnion }}>
      {children}
    </UnionContext.Provider>
  )
}

export const useUnion = (): UnionContextType => {
  const context = useContext(UnionContext)
  if (context === undefined) {
    throw new Error('useUnion must be used within a UnionProvider')
  }
  return context
}
