import axios from 'axios'
import { AppConfig } from '../../configs/appConfig'
import { USER_API_URI } from '../../constants/apiUri'
import BaseAuthService from './baseAuthService'

const api = axios.create({
  baseURL: AppConfig.apiBaseUrl,
  headers: { 'Content-Type': 'application/json' },
})

const API_LIST_NOT_HANDLE_WHEN_UNAUTHORIZED = [
  USER_API_URI.auth.login,
  USER_API_URI.auth.register,
]

api.interceptors.request.use(
  (config) => {
    const accessToken = BaseAuthService.getAccessToken()
    const token = typeof window !== 'undefined' ? accessToken : null

    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }

    return config
  },
  (error) => {
    return Promise.reject(error)
  },
)

api.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    if (
      error?.response?.status === 401 &&
      !API_LIST_NOT_HANDLE_WHEN_UNAUTHORIZED.includes(error.config.url)
    ) {
      BaseAuthService.removeAccessToken()
    }
    return Promise.reject(error)
  },
)

export default api
