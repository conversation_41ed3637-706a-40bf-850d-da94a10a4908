import CookieService from './cookieService'

class BaseAuthService {
  public static ACCESS_TOKEN = 'access_token'

  static getAccessToken() {
    return CookieService.get(this.ACCESS_TOKEN)
  }

  static setAccessToken(token: string) {
    return CookieService.set(this.ACCESS_TOKEN, token)
  }

  static removeAccessToken() {
    return CookieService.remove(this.ACCESS_TOKEN)
  }

  static isAuthenticated() {
    return !!this.getAccessToken()
  }
}

export default BaseAuthService
