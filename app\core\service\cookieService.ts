import Cookies from 'js-cookie'
import { AppConfig } from '../../configs/appConfig'

class CookieService {
  static set(name: string, value: string, options = {}) {
    Cookies.set(name, value, {
      secure: AppConfig.environment === 'production',
      sameSite: 'strict',
      path: '/',
      ...options,
    })
  }

  static get(name: string) {
    return Cookies.get(name)
  }

  static remove(name: string) {
    Cookies.remove(name)
  }
}

export default CookieService
