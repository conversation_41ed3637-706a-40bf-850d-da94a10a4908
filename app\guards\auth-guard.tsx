import { useAuth0 } from '@auth0/auth0-react'
import { useEffect } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { Outlet, useNavigate } from 'react-router'
import Loading from '../components/common/Loading'
import { TitleWrapper } from '../components/common/TitleWrapper'
import BaseAuthService from '../core/service/baseAuthService'
import AnalyticsService from '../services/analyticsService'
import { getUser } from '../services/userService'
import { setUser } from '../store/authSlice'
import type { RootState } from '../store/store'

type AuthGuardProps = {
  children?: React.ReactNode
  titleCustom?: string
}

const AuthGuard = ({ children, titleCustom }: AuthGuardProps) => {
  const dispatch = useDispatch()
  const navigate = useNavigate()
  const { isLoading, getAccessTokenSilently, user, isAuthenticated } =
    useAuth0()
  const currentUser = useSelector((state: RootState) => state.auth.user)

  useEffect(() => {
    const checkAuth = async () => {
      if (isLoading) return

      if (!isAuthenticated || !user) {
        BaseAuthService.removeAccessToken()
        navigate('/login', { replace: true })
        return
      }

      try {
        const token = await getAccessTokenSilently()
        BaseAuthService.setAccessToken(token)

        if (!currentUser) {
          const userData = await getUser()
          dispatch(setUser(userData))
          AnalyticsService.setUserParameters(userData)
        } else {
          AnalyticsService.setUserParameters(currentUser)
        }
      } catch {
        BaseAuthService.removeAccessToken()
        navigate('/login', { replace: true })
      }
    }

    checkAuth()
  }, [
    isLoading,
    getAccessTokenSilently,
    navigate,
    dispatch,
    currentUser,
    isAuthenticated,
    user,
  ])

  if (isLoading && isAuthenticated) return <Loading />

  return (
    <TitleWrapper titleCustom={titleCustom}>
      {children || <Outlet />}
    </TitleWrapper>
  )
}

export default AuthGuard
