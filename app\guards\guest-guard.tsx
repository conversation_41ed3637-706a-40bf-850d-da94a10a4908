import { useAuth0 } from '@auth0/auth0-react'
import { useEffect } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { Navigate, Outlet } from 'react-router'
import { TitleWrapper } from '../components/common/TitleWrapper'
import { getUser } from '../services/userService'
import { setUser } from '../store/authSlice'
import type { RootState } from '../store/store'

type GuestGuardProps = {
  children?: React.ReactNode
  titleCustom?: string
}

const GuestGuard = ({ children, titleCustom }: GuestGuardProps) => {
  const dispatch = useDispatch()
  const { isAuthenticated, isLoading, user } = useAuth0()
  const currentUser = useSelector((state: RootState) => state.auth.user)

  useEffect(() => {
    if (isAuthenticated && user && !currentUser) {
      getUser().then((userData) => {
        dispatch(setUser(userData))
      })
    }
  }, [isAuthenticated, user, currentUser, dispatch])

  // Show loading while Auth0 is checking authentication
  if (isLoading) {
    return <div>Loading...</div>
  }

  // If user is authenticated, redirect to home
  if (isAuthenticated && user) {
    return <Navigate to={'/'} replace={true} />
  }

  return (
    <TitleWrapper titleCustom={titleCustom}>
      {children || <Outlet />}
    </TitleWrapper>
  )
}

export default GuestGuard
