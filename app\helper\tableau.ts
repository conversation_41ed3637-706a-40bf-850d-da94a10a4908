import type React from 'react'
import FILTER from '../constants/filter'
import { TABLEAU_STORAGE_KEYS } from '../constants/tableauLinks'

export type TableauAPI = {
  TableauEventType: any
  FilterDomainType: any
  FilterType: any
  SheetType: any
}

export type TableauUser = {
  emplrId?: number
  jigyosyoCd?: string
  [key: string]: string | number | boolean | object | undefined
}

export type HTMLTableauVizElement = {
  exportCrosstabAsync(name: string, crosstabFileFormat: string): unknown
  exportPDFAsync(name: string[], options?: any): Promise<Blob>
  token?: string
  workbook?: {
    changeParameterValueAsync(arg0: string, arg1: any): Promise<unknown>
    getParametersAsync(): Promise<any[]>
    activeSheet: any
    publishedSheetsInfo: any
  }
  selectedDataForExport?: {
    columns: Array<{ fieldName: string }>
    data: Array<Array<{ formattedValue: string }>>
    worksheetName: string
  } | null
  isLoaded?: boolean
  isInitialized?: boolean
  addEventListener(
    type: string,
    listener: (event: Event) => void,
    options?: boolean | AddEventListenerOptions,
  ): void
  removeEventListener(
    type: string,
    listener: (event: Event) => void,
    options?: boolean | EventListenerOptions,
  ): void
} & HTMLElement

export enum FilterUpdateType {
  All = 'all',
  Replace = 'replace',
  Add = 'add',
  Remove = 'remove',
}

export let tableauApiCache: TableauAPI | null = null

export const findWorkSheet = (
  workbook: any,
  activeSheet: any,
  sheetName?: string,
) => {
  if (!workbook || !activeSheet) return null

  if (activeSheet.worksheets) {
    const graphSheet = activeSheet.worksheets.find((sheet: any) =>
      sheet.name.includes(sheetName || 'Graph'),
    )
    return graphSheet || activeSheet
  }

  return activeSheet
}

export const debounce = <F extends (...args: any[]) => any>(
  func: F,
  wait: number,
): ((this: any, ...args: Parameters<F>) => void) => {
  let timeoutId: number | null = null
  return function (this: any, ...args: Parameters<F>) {
    const context = this
    if (timeoutId) clearTimeout(timeoutId)
    timeoutId = window.setTimeout(() => func.apply(context, args), wait)
  } as F
}

export const loadTableauApi = async (): Promise<TableauAPI> => {
  if (tableauApiCache) return tableauApiCache
  try {
    const api = (await import(
      // @ts-expect-error
      'https://viz.kensuke-plus.dev.jmdc.co.jp/javascripts/api/tableau.embedding.3.latest.js'
    )) as TableauAPI
    tableauApiCache = api
    return api
  } catch (error) {
    return {
      TableauEventType: {},
      FilterDomainType: {},
      FilterType: {},
      SheetType: {},
    } as TableauAPI
  }
}

export const loadSummaryData = (
  sheet: any,
  setData: React.Dispatch<React.SetStateAction<any[] | null>>,
  storageKey?: string,
) => {
  if (!sheet?.getSummaryDataAsync) return

  sheet
    .getSummaryDataAsync()
    .then((dataTable: { data: any[] | null }) => {
      if (dataTable?.data) {
        setData(dataTable.data)
        if (storageKey) {
          localStorage.setItem(storageKey, JSON.stringify(dataTable.data))
        }
      }
    })
    .catch((error: any) => {
      console.error('Error loading summary data', error)
    })
}

export const parseFilters = async (
  filters: any[],
  FilterType: any,
  setUniqueFilters: React.Dispatch<React.SetStateAction<any[] | null>>,
  onFiltersChange?: (filters: any[]) => void,
  parameters?: any[],
) => {
  if (!filters?.length) {
    const emptyResult = parameters || []
    setUniqueFilters(emptyResult)
    onFiltersChange?.(emptyResult)
    return
  }

  const uniqueFiltersMap = new Map()
  const processedFieldNames = new Set()

  const validFilters = filters.filter(
    (filter) =>
      filter?.fieldName &&
      !processedFieldNames.has(filter.fieldName) &&
      filter.fieldName !== FILTER.emplrId &&
      filter.fieldName !== FILTER.jigyosyoCd,
  )

  if (!validFilters.length) {
    const result = parameters || []
    setUniqueFilters(result)
    onFiltersChange?.(result)
    return
  }

  validFilters.forEach((filter) => processedFieldNames.add(filter.fieldName))

  const filterPromises = validFilters.map(async (filter) => {
    try {
      const domain = await filter.getDomainAsync('relevant')
      filter.domain =
        filter.filterType === FilterType.Range ?
          { min: domain.min, max: domain.max }
        : {
            values:
              domain.values?.map((item: { value: string }) => item.value) || [],
          }
      uniqueFiltersMap.set(filter.fieldId, filter)
    } catch (error) {
      console.error(`Error processing filter ${filter.fieldName}:`, error)
    }
  })

  await Promise.allSettled(filterPromises).catch((error: any) => {
    console.error('Error processing filters:', error)
  })

  const uniqueFiltersArray = [
    ...uniqueFiltersMap.values(),
    ...(parameters || []),
  ]
  setUniqueFilters(uniqueFiltersArray)
  onFiltersChange?.(uniqueFiltersArray)
}

export const setupMarkSelectionListener = (
  viz: HTMLTableauVizElement,
  TableauEventType: any,
  SheetType: any,
) => {
  viz.addEventListener(
    TableauEventType.MarkSelectionChanged,
    async (_event) => {
      const dashboard = viz?.workbook?.activeSheet
      if (!dashboard || dashboard.sheetType !== SheetType.Dashboard) return

      for (const worksheet of dashboard.worksheets) {
        const marks = await worksheet
          .getSelectedMarksAsync()
          .catch(() => ({ data: [] }))
        if (!marks.data.length) continue

        const summaryData = await worksheet
          .getSummaryDataAsync()
          .catch(() => ({ columns: [], data: [] }))
        if (!summaryData) continue

        const columns =
          summaryData.columns?.map((c: { fieldName: string }) => c.fieldName) ||
          []
        const formattedData =
          summaryData.data?.map((row: Array<{ formattedValue: string }>) =>
            row.map((cell) => cell.formattedValue),
          ) || []

        viz.selectedDataForExport = {
          columns,
          data: formattedData,
          worksheetName: worksheet.name,
        }
        break
      }
    },
  )
}

export const applySpecialFilters = async (
  activeSheet: any,
  user: TableauUser | null,
  FilterUpdateType: any,
) => {
  if (!activeSheet || !user) return

  if (!user.emplrId && !user.jigyosyoCd) return

  const specialFilterPromises = []

  if (user.emplrId) {
    specialFilterPromises.push(
      activeSheet
        .applyFilterAsync(
          FILTER.emplrId,
          [String(user.emplrId)],
          FilterUpdateType.Replace,
        )
        .catch(() => null),
    )
  }

  specialFilterPromises.push(
    activeSheet
      .applyFilterAsync(
        FILTER.jigyosyoCd,
        user.hierarchyLevel == 3 && user.jigyosyoCd ? [user.jigyosyoCd] : [],
        user.hierarchyLevel == 3 && user.jigyosyoCd ?
          FilterUpdateType.Replace
        : FilterUpdateType.All,
      )
      .catch(() => null),
  )

  await Promise.all(specialFilterPromises)
}

export const applyParametersAndRegularFilters = async (
  workbook: any,
  activeSheet: any,
  filters: any[],
  filterValues: Record<string, any>,
  FilterUpdateType: any,
  changedKeys?: string[],
  onFiltersChange?: (filters: any[]) => void,
) => {
  if (
    !workbook ||
    !activeSheet ||
    !filters?.length ||
    !Object.keys(filterValues).length
  )
    return

  localStorage.setItem(TABLEAU_STORAGE_KEYS.IS_LOADING_TABLEAU, 'true')

  const parameterPromises: Promise<any>[] = []

  await workbook
    .getParametersAsync()
    .then((parameters: any[]) => {
      const parameterNames = parameters.map((param) => param.name)

      Object.entries(filterValues).forEach(([key, value]) => {
        if (
          parameterNames.includes(key) &&
          (!changedKeys || changedKeys.includes(key))
        ) {
          parameterPromises.push(workbook.changeParameterValueAsync(key, value))
        }
      })

      return Promise.all(parameterPromises)
    })
    .catch(() => {})

  const memoizedFilters = { ...filterValues }

  const regularFilterPromises = filters
    .filter(
      (filter) =>
        filter.fieldName &&
        memoizedFilters[filter.fieldName] !== undefined &&
        (!changedKeys || changedKeys.includes(filter.fieldName)),
    )
    .map((filter) => {
      const filterValue = memoizedFilters[filter.fieldName]

      if (filter.filterType === 'range') {
        return activeSheet
          .applyRangeFilterAsync(filter.fieldName, {
            min: filterValue.min,
            max: filterValue.max,
          })
          .catch(() => null)
      }

      if (
        filterValue === 0 ||
        (Array.isArray(filterValue) && !filterValue.length)
      ) {
        return activeSheet
          .applyFilterAsync(
            filter.fieldName,
            [],
            filterValue === 0 ? FilterUpdateType.All : FilterUpdateType.Replace,
          )
          .catch(() => null)
      }

      const valuesToApply =
        Array.isArray(filterValue) ?
          filterValue.map((v) => String(v))
        : [String(filterValue)]

      return activeSheet
        .applyFilterAsync(
          filter.fieldName,
          valuesToApply,
          FilterUpdateType.Replace,
        )
        .catch(() => null)
    })

  if (regularFilterPromises.length) {
    await Promise.all(regularFilterPromises)
    localStorage.setItem(TABLEAU_STORAGE_KEYS.IS_LOADING_TABLEAU, 'false')
  }

  localStorage.setItem(TABLEAU_STORAGE_KEYS.IS_LOADING_TABLEAU, 'false')

  if (changedKeys?.includes(FILTER.filterType)) {
    const modalFilter = filters.find((f: any) => f.fieldName === FILTER.modal)

    if (modalFilter) {
      const domain = await modalFilter.getDomainAsync()
      modalFilter.domain.values = domain.values.map(
        (v: { value: string }) => v.value,
      )
      onFiltersChange?.([...filters, modalFilter])
    }
  }
}

export const setupEventListeners = (
  viz: HTMLTableauVizElement,
  eventHandlers: { [key: string]: (event: Event) => void },
) => {
  Object.entries(eventHandlers).forEach(([event, listener]) => {
    if (event && listener) {
      viz.removeEventListener(event, listener)
    }
  })

  Object.entries(eventHandlers).forEach(([event, listener]) => {
    if (event && listener) {
      viz.addEventListener(event, listener)
    }
  })

  return eventHandlers
}
