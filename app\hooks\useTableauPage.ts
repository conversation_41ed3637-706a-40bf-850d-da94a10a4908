import { useState } from 'react'

export type UseTableauPageReturn = {
  filters: any[]
  tableauFilters: Record<string, any>
  exportType: string
  exportTrigger: number
  setFilters: (filters: any[]) => void
  handleFilterChange: (filterName: string, value: any) => void
  handleExport: (type: string) => void
}

export const useTableauPage = (): UseTableauPageReturn => {
  const [filters, setFilters] = useState<any[]>([])
  const [tableauFilters, setTableauFilters] = useState<Record<string, any>>({})
  const [exportType, setExportType] = useState<string>('')
  const [exportTrigger, setExportTrigger] = useState<number>(0)

  const handleFilterChange = (filterName: string, value: any) => {
    setTableauFilters((prev) => ({
      ...prev,
      [filterName]: value,
    }))
  }

  const handleExport = (type: string) => {
    setExportType(type)
    setExportTrigger((prev) => prev + 1)
  }

  return {
    filters,
    tableauFilters,
    exportType,
    exportTrigger,
    setFilters,
    handleFilterChange,
    handleExport,
  }
}
