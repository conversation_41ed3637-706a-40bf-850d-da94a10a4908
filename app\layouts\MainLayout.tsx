import { useAuth0 } from '@auth0/auth0-react'
import type { ReactNode } from 'react'
import { useEffect } from 'react'
import { useSelector } from 'react-redux'
import { useLocation, useNavigate } from 'react-router'
import Loading from '../components/common/Loading'
import { Box } from '../components/common/MaterialUI'
import Footer from '../components/layout/Footer'
import Header from '../components/layout/Header'
import { Sidebar } from '../components/layout/Sidebar'
import { pathsHasFooter } from '../configs/appConfig'
import { useUnion } from '../contexts/UnionContext'
import type { RootState } from '../store/store'

type LayoutProps = {
  children: ReactNode
}

export const LayoutContent = ({ children }: LayoutProps) => {
  const { user, isLoading, isAuthenticated } = useAuth0()
  const currentUser = useSelector((state: RootState) => state.auth.user)
  const location = useLocation()
  const navigate = useNavigate()
  const hasFooter = pathsHasFooter.includes(location.pathname)

  useEffect(() => {
    const checkUnion = () => {
      if (currentUser?.hierarchyLevel === 1 && location.pathname !== '/') {
        const unionInfo = localStorage.getItem('union_selected_info')
        let hasValidUnion = false

        if (unionInfo) {
          try {
            const { expire } = JSON.parse(unionInfo)
            hasValidUnion = expire && Date.now() < expire
          } catch {
            hasValidUnion = false
          }
        }

        if (!hasValidUnion && isAuthenticated) {
          navigate('/')
        }
      }
    }

    checkUnion()
  }, [currentUser, location.pathname, navigate, isAuthenticated])

  // Show layout for authenticated users, except for auth pages
  const isAuthPage = location.pathname === '/login'
  const showLayout = isAuthenticated && !isAuthPage
  const showSidebar = user && showLayout && currentUser

  const paddingStyles =
    showSidebar ?
      { paddingY: 3, paddingLeft: '36px', paddingRight: '40px' }
    : { paddingY: 0, paddingLeft: 0, paddingRight: 0 }

  const { unionRefreshKey } = useUnion()

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', height: '100vh' }}>
      {isLoading ?
        <Loading />
      : <>
          {showLayout && (
            <Header isShowMenu={showLayout} isShowText={showLayout} />
          )}
          <Box sx={{ display: 'flex', flexGrow: 1, overflow: 'hidden' }}>
            {showSidebar && <Sidebar />}
            <Box
              key={unionRefreshKey}
              sx={{ flexGrow: 1, overflow: 'auto', ...paddingStyles }}
            >
              {children}
            </Box>
          </Box>
          {(hasFooter || !showLayout) && <Footer />}
        </>
      }
    </Box>
  )
}
