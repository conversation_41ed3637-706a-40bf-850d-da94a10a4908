import {
  <PERSON><PERSON>,
  <PERSON>a,
  Outlet,
  <PERSON><PERSON><PERSON>,
  ScrollRestoration,
  useLocation,
  useNavigate,
} from 'react-router'

import { Auth0Provider, useAuth0 } from '@auth0/auth0-react'
import { Provider, useSelector } from 'react-redux'
import type { Route } from './+types/root'
import './app.css'
import Loading from './components/common/Loading'
import { AppConfig, pathsHasFooter, routeUrl } from './configs/appConfig'
import type { RootState } from './store/store'
import { store } from './store/store'

import { StrictMode, useEffect } from 'react'
import { Box } from './components/common/MaterialUI'
import Footer from './components/layout/Footer'
import Header from './components/layout/Header'
import { Sidebar } from './components/layout/Sidebar'
import { UnionProvider, useUnion } from './contexts/UnionContext'
import { createRoutesFromConfig } from './helper/helper'

export const links: Route.LinksFunction = () => [
  { rel: 'icon', href: '/favicon02.ico' },
  { rel: 'preconnect', href: 'https://fonts.googleapis.com' },
  {
    rel: 'preconnect',
    href: 'https://fonts.gstatic.com',
    crossOrigin: 'anonymous',
  },
  {
    rel: 'stylesheet',
    href: 'https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap',
  },
]

const RemoveTrailingSlash = () => {
  const location = useLocation()
  const navigate = useNavigate()

  useEffect(() => {
    if (location.pathname !== '/' && location.pathname.endsWith('/')) {
      navigate(location.pathname.slice(0, -1), { replace: true })
    }
  }, [location.pathname, navigate])

  return null
}

const LayoutContent = () => {
  const { isLoading, isAuthenticated } = useAuth0()
  const currentUser = useSelector((state: RootState) => state.auth.user)
  const location = useLocation()
  const navigate = useNavigate()
  const hasFooter = pathsHasFooter.includes(location.pathname)

  useEffect(() => {
    const checkUnion = () => {
      if (currentUser?.hierarchyLevel === 1 && location.pathname !== '/') {
        const unionInfo = localStorage.getItem('union_selected_info')
        let hasValidUnion = false

        if (unionInfo) {
          try {
            const { expire } = JSON.parse(unionInfo)
            hasValidUnion = expire && Date.now() < expire
          } catch {
            hasValidUnion = false
          }
        }

        if (!hasValidUnion && isAuthenticated) {
          navigate('/')
        }
      }
    }

    checkUnion()
  }, [currentUser, location.pathname, navigate, isAuthenticated])

  const validPaths = createRoutesFromConfig(routeUrl)
  const isValidPath = validPaths.some((path) => {
    const regex = new RegExp(`^${path}/?$`)
    return regex.test(location.pathname)
  })

  const isDynamicNoticeRoute = /^\/notices\/\d+\/?$/.test(location.pathname)
  const showLayout =
    isValidPath || isDynamicNoticeRoute || location.pathname === '/'

  // Simplified sidebar logic - show immediately if authenticated
  const showSidebar = isAuthenticated && showLayout && !isLoading

  const paddingStyles =
    showSidebar ?
      { paddingY: 3, paddingLeft: '36px', paddingRight: '40px' }
    : { paddingY: 0, paddingLeft: 0, paddingRight: 0 }

  const { unionRefreshKey } = useUnion()

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', height: '100vh' }}>
      {isLoading ?
        <Loading />
      : <>
          {showLayout && (
            <Header isShowMenu={showLayout} isShowText={showLayout} />
          )}
          <Box sx={{ display: 'flex', flexGrow: 1, overflow: 'hidden' }}>
            {showSidebar && <Sidebar />}
            <Box
              key={unionRefreshKey}
              sx={{ flexGrow: 1, overflow: 'auto', ...paddingStyles }}
            >
              <Outlet />
            </Box>
          </Box>
          {(hasFooter || !showLayout) && <Footer />}
        </>
      }
    </Box>
  )
}

export function Layout({ children }: { children: React.ReactNode }) {
  return (
    <html lang={'ja'}>
      <head>
        <meta charSet={'utf-8'} />
        <meta
          name={'viewport'}
          content={'width=device-width, initial-scale=1'}
        />
        <title>らくらく健助</title>
        <Meta />
        <Links />
      </head>
      <body>
        {children}
        <ScrollRestoration />
        <Scripts />
      </body>
    </html>
  )
}

export function HydrateFallback() {
  return <Loading />
}

export default function App() {
  return (
    <Auth0Provider
      domain={AppConfig.auth0Domain}
      clientId={AppConfig.auth0ClientId}
      authorizationParams={{
        redirect_uri: window.location.origin,
        audience: `https://${AppConfig.auth0Domain}/api/v2/`,
      }}
      cacheLocation={'localstorage'}
    >
      <Provider store={store}>
        <StrictMode>
          <UnionProvider>
            <RemoveTrailingSlash />
            <LayoutContent />
          </UnionProvider>
        </StrictMode>
      </Provider>
    </Auth0Provider>
  )
}
