import type { RouteConfig } from '@react-router/dev/routes'
import { index, layout, prefix, route } from '@react-router/dev/routes'
import { routeUrl } from './configs/appConfig'

export default [
  route(routeUrl.NOT_FOUND, './routes/not-found.tsx'),
  layout('guards/guest-guard.tsx', [
    route(routeUrl.LOGIN, './routes/auth/login/login.page.tsx'),
  ]),
  layout('guards/auth-guard.tsx', [
    route(routeUrl.PROFILE, './routes/profile/profile.page.tsx'),
    route(
      routeUrl.HEALTH_CHECK_FORM.path,
      './routes/health-check-form/health-check-form.tsx',
    ),
    route(routeUrl.MEMBER_INFO.path, './routes/member-info/member-info.tsx'),
    route(
      routeUrl.HEALTH_CARE_MANAGEMENT.path,
      './routes/health-care-management/health-care-management.tsx',
    ),
    route(
      routeUrl.REGULAR_REPORT.path,
      './routes/regular-report/regular-report.tsx',
    ),
    route(
      routeUrl.DOWNLOAD_LIST.path,
      './routes/dowload-list/download-list.tsx',
    ),
    route(routeUrl.HOME, './routes/home/<USER>'),
    route(routeUrl.RECEIPT.path, './routes/receipt/receipt.tsx'),
    route(routeUrl.NOTICE.path, './routes/notice/notice.tsx'),
    route('*', './routes/not-found/not-found.tsx'),

    ...prefix(routeUrl.RECEIPT.path, [
      route(
        routeUrl.RECEIPT.children.EXAMINATION_STATUS.path,
        './routes/receipt/examination-status/examination-status.tsx',
      ),
      ...prefix(routeUrl.RECEIPT.children.MEDICAL_EXPENSE.path, [
        layout('./routes/receipt/medical-expense/medical-expense.tsx', [
          index('./routes/receipt/medical-expense/classification/navigate.tsx'),
          route(
            routeUrl.RECEIPT.children.MEDICAL_EXPENSE.children.CLASSIFICATION,
            './routes/receipt/medical-expense/classification/classification.tsx',
          ),
          route(
            routeUrl.RECEIPT.children.MEDICAL_EXPENSE.children.COUNT,
            './routes/receipt/medical-expense/count/count.tsx',
          ),
          route(
            routeUrl.RECEIPT.children.MEDICAL_EXPENSE.children.ANNUAL_RANGE,
            './routes/receipt/medical-expense/annual-range/annual-range.tsx',
          ),
          route(
            routeUrl.RECEIPT.children.MEDICAL_EXPENSE.children.AGE_GROUP,
            './routes/receipt/medical-expense/age-group/age-group.tsx',
          ),
          route(
            routeUrl.RECEIPT.children.MEDICAL_EXPENSE.children.COST_ANALYSIS,
            './routes/receipt/medical-expense/cost-analysis/cost-analysis.tsx',
          ),
          route(
            routeUrl.RECEIPT.children.MEDICAL_EXPENSE.children
              .DISEASE_CLASSIFICATION,
            './routes/receipt/medical-expense/disease-classification/disease-classification.tsx',
          ),
          route(
            routeUrl.RECEIPT.children.MEDICAL_EXPENSE.children
              .SPECIFIC_DISEASE_RATIO,
            './routes/receipt/medical-expense/specific-disease-ratio/specific-disease-ratio.tsx',
          ),
        ]),
      ]),
      ...prefix(routeUrl.RECEIPT.children.LIFESTYLE_DISEASE.path, [
        layout('./routes/receipt/lifestyle-disease/lifestyle-disease.tsx', [
          index(
            './routes/receipt/lifestyle-disease/expense-trend/navigate.tsx',
          ),
          route(
            routeUrl.RECEIPT.children.LIFESTYLE_DISEASE.children.EXPENSE_TREND,
            './routes/receipt/lifestyle-disease/expense-trend/expense-trend.tsx',
          ),
          route(
            routeUrl.RECEIPT.children.LIFESTYLE_DISEASE.children.PATIENT_COUNT,
            './routes/receipt/lifestyle-disease/patient-count/patient-count.tsx',
          ),
          route(
            routeUrl.RECEIPT.children.LIFESTYLE_DISEASE.children
              .AGE_GROUP_RATIO,
            './routes/receipt/lifestyle-disease/age-group-ratio/age-group-ratio.tsx',
          ),
        ]),
      ]),
      ...prefix(routeUrl.RECEIPT.children.CANCER.path, [
        layout('./routes/receipt/cancer/cancer.tsx', [
          index('./routes/receipt/cancer/expense-trend/navigate.tsx'),
          route(
            routeUrl.RECEIPT.children.CANCER.children.EXPENSE_TREND,
            './routes/receipt/cancer/expense-trend/expense-trend.tsx',
          ),
          route(
            routeUrl.RECEIPT.children.CANCER.children.PATIENT_COUNT,
            './routes/receipt/cancer/patient-count/patient-count.tsx',
          ),
          route(
            routeUrl.RECEIPT.children.CANCER.children.AGE_GROUP_RATIO,
            './routes/receipt/cancer/age-group-ratio/age-group-ratio.tsx',
          ),
        ]),
      ]),
      ...prefix(routeUrl.RECEIPT.children.DENTAL.path, [
        layout('./routes/receipt/dental/dental.tsx', [
          index('./routes/receipt/dental/expense-trend/navigate.tsx'),
          route(
            routeUrl.RECEIPT.children.DENTAL.children.EXPENSE_TREND,
            './routes/receipt/dental/expense-trend/expense-trend.tsx',
          ),
          route(
            routeUrl.RECEIPT.children.DENTAL.children.PATIENT_COUNT,
            './routes/receipt/dental/patient-count/patient-count.tsx',
          ),
          route(
            routeUrl.RECEIPT.children.DENTAL.children.AGE_GROUP_RATIO,
            './routes/receipt/dental/age-group-ratio/age-group-ratio.tsx',
          ),
        ]),
      ]),
    ]),
    ...prefix(routeUrl.HEALTH_CHECK_FORM.path, [
      route(
        routeUrl.HEALTH_CHECK_FORM.children.CHECKUP_RATE.path,
        './routes/health-check-form/checkup-rate/checkup-rate.tsx',
      ),
      route(
        routeUrl.HEALTH_CHECK_FORM.children.METABOLIC_SYNDROME.path,
        './routes/health-check-form/metabolic-syndrome/metabolic-syndrome.tsx',
      ),
      route(
        routeUrl.HEALTH_CHECK_FORM.children.HEALTH_ISSUE_MAP.path,
        './routes/health-check-form/health-issue-map/health-issue-map.tsx',
      ),
      ...prefix(routeUrl.HEALTH_CHECK_FORM.children.ITEM_DETAILS.path, [
        layout('./routes/health-check-form/item-details/item-details.tsx', [
          index(
            './routes/health-check-form/item-details/health-check/navigate.tsx',
          ),
          route(
            routeUrl.HEALTH_CHECK_FORM.children.ITEM_DETAILS.children
              .HEALTH_CHECK,
            './routes/health-check-form/item-details/health-check/health-check.tsx',
          ),
          route(
            routeUrl.HEALTH_CHECK_FORM.children.ITEM_DETAILS.children
              .QUESTIONNAIRE,
            './routes/health-check-form/item-details/questionnaire/questionnaire.tsx',
          ),
          route(
            routeUrl.HEALTH_CHECK_FORM.children.ITEM_DETAILS.children
              .HEALTH_CHECK_RISK,
            './routes/health-check-form/item-details/health-check-risk/health-check-risk.tsx',
          ),
          route(
            routeUrl.HEALTH_CHECK_FORM.children.ITEM_DETAILS.children
              .QUESTIONNAIRE_RISK,
            './routes/health-check-form/item-details/questionnaire-risk/questionnaire-risk.tsx',
          ),
        ]),
      ]),
      ...prefix(routeUrl.HEALTH_CHECK_FORM.children.OUT_OF_STANDARD.path, [
        layout(
          './routes/health-check-form/out-of-standard/out-of-standard.tsx',
          [
            index(
              './routes/health-check-form/out-of-standard/health-check-rate/navigate.tsx',
            ),
            route(
              routeUrl.HEALTH_CHECK_FORM.children.OUT_OF_STANDARD.children
                .HEALTH_CHECK_RATE,
              './routes/health-check-form/out-of-standard/health-check-rate/health-check-rate.tsx',
            ),
            route(
              routeUrl.HEALTH_CHECK_FORM.children.OUT_OF_STANDARD.children
                .QUESTIONNAIRE_RATE,
              './routes/health-check-form/out-of-standard/questionnaire-rate/questionnaire-rate.tsx',
            ),
          ],
        ),
      ]),
    ]),
    ...prefix(routeUrl.MEMBER_INFO.path, [
      route(
        routeUrl.MEMBER_INFO.children.MEMBER_COUNT.path,
        './routes/member-info/member-count/member-count.tsx',
      ),
      route(
        routeUrl.MEMBER_INFO.children.AVERAGE_AGE.path,
        './routes/member-info/average-age/average-age.tsx',
      ),
      ...prefix(routeUrl.MEMBER_INFO.children.MEMBER_COMPOSITION.path, [
        layout(
          './routes/member-info/member-composition/member-composition.tsx',
          [
            index(
              './routes/member-info/member-composition/attribute-composition/navigate.tsx',
            ),
            route(
              routeUrl.MEMBER_INFO.children.MEMBER_COMPOSITION.children
                .ATTRIBUTE_COMPOSITION,
              './routes/member-info/member-composition/attribute-composition/attribute-composition.tsx',
            ),
            route(
              routeUrl.MEMBER_INFO.children.MEMBER_COMPOSITION.children
                .AGE_GROUP_COMPOSITION,
              './routes/member-info/member-composition/age-group-composition/age-group-composition.tsx',
            ),
            route(
              routeUrl.MEMBER_INFO.children.MEMBER_COMPOSITION.children
                .GENDER_COMPOSITION,
              './routes/member-info/member-composition/gender-composition/gender-composition.tsx',
            ),
          ],
        ),
      ]),
    ]),
    ...prefix(routeUrl.HEALTH_CARE_MANAGEMENT.path, [
      route(
        routeUrl.HEALTH_CARE_MANAGEMENT.children.CANCER.path,
        './routes/health-care-management/cancer/cancer.tsx',
      ),
      ...prefix(
        routeUrl.HEALTH_CARE_MANAGEMENT.children.SPECIFIC_HEALTH_CHECK.path,
        [
          layout(
            './routes/health-care-management/specific-health-check/specific-health-check.tsx',
            [
              index(
                './routes/health-care-management/specific-health-check/specific-health-check-rate/navigate.tsx',
              ),
              route(
                routeUrl.HEALTH_CARE_MANAGEMENT.children.SPECIFIC_HEALTH_CHECK
                  .children.SPECIFIC_HEALTH_CHECK_RATE,
                './routes/health-care-management/specific-health-check/specific-health-check-rate/specific-health-check-rate.tsx',
              ),
              route(
                routeUrl.HEALTH_CARE_MANAGEMENT.children.SPECIFIC_HEALTH_CHECK
                  .children.TARGET_POPULATION_RATE,
                './routes/health-care-management/specific-health-check/target-population-rate/target-population-rate.tsx',
              ),
              route(
                routeUrl.HEALTH_CARE_MANAGEMENT.children.SPECIFIC_HEALTH_CHECK
                  .children.OBESITY_REDUCTION_RATE,
                './routes/health-care-management/specific-health-check/obesity-reduction-rate/obesity-reduction-rate.tsx',
              ),
            ],
          ),
        ],
      ),
      ...prefix(
        routeUrl.HEALTH_CARE_MANAGEMENT.children.RECOMMENDED_HEALTH_CHECK.path,
        [
          layout(
            './routes/health-care-management/recommended-health-check/recommended-health-check.tsx',
            [
              index(
                './routes/health-care-management/recommended-health-check/lifestyle-disease-treatment-holder/navigate.tsx',
              ),
              route(
                routeUrl.HEALTH_CARE_MANAGEMENT.children
                  .RECOMMENDED_HEALTH_CHECK.children
                  .LIFESTYLE_DISEASE_TREATMENT_HOLDERS,
                './routes/health-care-management/recommended-health-check/lifestyle-disease-treatment-holder/lifestyle-disease-treatment-holder.tsx',
              ),
              route(
                routeUrl.HEALTH_CARE_MANAGEMENT.children
                  .RECOMMENDED_HEALTH_CHECK.children
                  .DIABETES_UNCONTROLLED_PATIENTS,
                './routes/health-care-management/recommended-health-check/diabetes-uncontrolled-patients/diabetes-uncontrolled-patients.tsx',
              ),
            ],
          ),
        ],
      ),
      ...prefix(routeUrl.HEALTH_CARE_MANAGEMENT.children.DIABETES.path, [
        layout('./routes/health-care-management/diabetes/diabetes.tsx', [
          index(
            './routes/health-care-management/diabetes/kidney-risk-count/navigate.tsx',
          ),
          route(
            routeUrl.HEALTH_CARE_MANAGEMENT.children.DIABETES.children
              .KIDNEY_RISK_COUNT,
            './routes/health-care-management/diabetes/kidney-risk-count/kidney-risk-count.tsx',
          ),
          route(
            routeUrl.HEALTH_CARE_MANAGEMENT.children.DIABETES.children
              .ANNUAL_DIALYSIS_TREND,
            './routes/health-care-management/diabetes/annual-dialysis-trend/annual-dialysis-trend.tsx',
          ),
        ]),
      ]),
    ]),
    ...prefix(routeUrl.NOTICE.path, [
      route(':id', './routes/notice/notice-detail.tsx'),
    ]),
  ]),
] satisfies RouteConfig
