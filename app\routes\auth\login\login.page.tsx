import { useAuth0 } from '@auth0/auth0-react'
import Button from '../../../components/common/Button'
import { AppConfig } from '../../../configs/appConfig'
import title from '../../../constants/title'
import type { Route } from './+types/login.page'

// eslint-disable-next-line no-empty-pattern
export function meta({}: Route.MetaArgs) {
  return [{ title: title.login.loginTitle }]
}

const notices = [
  {
    id: 1,
    title: '障害のお知らせ',
    content:
      '8月12日（月）から8月19日(月)10:00（サービス停止措置以前）までの期間において、らくらく健助にて正しいデータ集計ができていない状況を検知しましたので、下記の通りご報告いたします。ご迷惑をおかけし誠に申し訳ございません。',
    createdAt: '2025/05/15',
  },
  {
    id: 2,
    title: '障害のお知らせ',
    content:
      '8月12日（月）から8月19日(月)10:00（サービス停止措置以前）までの期間において、らくらく健助にて正しいデータ集計ができていない状況を検知しましたので、下記の通りご報告いたします。ご迷惑をおかけし誠に申し訳ございません。',
    createdAt: '2025/05/15',
  },
  {
    id: 3,
    title: '障害のお知らせ',
    content:
      '8月12日（月）から8月19日(月)10:00（サービス停止措置以前）までの期間において、らくらく健助にて正しいデータ集計ができていない状況を検知しましたので、下記の通りご報告いたします。ご迷惑をおかけし誠に申し訳ございません。',
    createdAt: '2025/05/15',
  },
  {
    id: 4,
    title: '障害のお知らせ',
    content:
      '8月12日（月）から8月19日(月)10:00（サービス停止措置以前）までの期間において、らくらく健助にて正しいデータ集計ができていない状況を検知しましたので、下記の通りご報告いたします。ご迷惑をおかけし誠に申し訳ございません。',
    createdAt: '2025/05/15',
  },
  {
    id: 5,
    title: '障害のお知らせ',
    content:
      '8月12日（月）から8月19日(月)10:00（サービス停止措置以前）までの期間において、らくらく健助にて正しいデータ集計ができていない状況を検知しましたので、下記の通りご報告いたします。ご迷惑をおかけし誠に申し訳ございません。',
    createdAt: '2025/05/15',
  },
  {
    id: 6,
    title: '障害のお知らせ',
    content:
      '8月12日（月）から8月19日(月)10:00（サービス停止措置以前）までの期間において、らくらく健助にて正しいデータ集計ができていない状況を検知しましたので、下記の通りご報告いたします。ご迷惑をおかけし誠に申し訳ございません。',
    createdAt: '2025/05/15',
  },
]

const Login = () => {
  const { loginWithRedirect } = useAuth0()

  return (
    <div className={'flex min-h-[calc(100vh-108px)] flex-col bg-beige-400'}>
      <div
        className={
          'mx-auto mt-[73px] flex h-[120px] w-[400px] items-center justify-center h-sm:mt-[1%]'
        }
      >
        <img
          src={'/maintain-page/images/logo-login.png'}
          alt={'Quick Reference'}
        />
      </div>

      <div className={'mx-auto mt-[64px] w-[880px] h-sm:mt-[2%]'}>
        <p className={'text-base font-semibold text-gray-900'}>
          {title.login.maintainTitle}
        </p>
        <div className={'mt-2 rounded-xl border border-gray-300 bg-white p-4'}>
          <div
            className={
              'flex h-[240px] w-full flex-col gap-4 overflow-y-auto pr-4'
            }
          >
            {notices.map((notice, index) => (
              <div key={notice.id}>
                <p className={'text-xs text-gray-600'}>{notice.createdAt}</p>
                <p className={'text-sm font-semibold text-gray-900'}>
                  {notice.title}
                </p>
                <p className={'text-sm text-gray-900'}>{notice.content}</p>
                {index < notices.length - 1 && (
                  <hr
                    className={'mt-4 border-t border-dotted border-gray-300'}
                  />
                )}
              </div>
            ))}
          </div>
        </div>
      </div>

      <div className={'mt-[64px] flex flex-col items-center h-sm:mt-[2%]'}>
        {AppConfig.maintenanceMode ?
          <p className={'text-lg-bold text-red-700'}>
            {title.login.maintainDesc}
          </p>
        : <Button onClick={loginWithRedirect}>{title.button.login}</Button>}
      </div>
    </div>
  )
}

export default Login
