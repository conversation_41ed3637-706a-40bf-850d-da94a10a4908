import { useState } from 'react'
import { register } from '../../../services/authService'
const Register = () => {
  const [name, setName] = useState('')
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')

  const handleRegister = async () => {
    try {
      await register({ name, email, password })
      alert('Register successful!')
    } catch (error) {
      alert(error)
    }
  }

  return (
    <div className={'p-4'}>
      <h2 className={'text-2xl font-semibold '}>Register</h2>
      <input
        type={'text'}
        placeholder={'Name'}
        onChange={(e) => setName(e.target.value)}
      />
      <input
        type={'email'}
        placeholder={'Email'}
        onChange={(e) => setEmail(e.target.value)}
      />
      <input
        type={'password'}
        placeholder={'Password'}
        onChange={(e) => setPassword(e.target.value)}
      />
      <button
        onClick={handleRegister}
        className={'bg-green-500 p-2 text-white'}
      >
        Register
      </button>
    </div>
  )
}

export default Register
