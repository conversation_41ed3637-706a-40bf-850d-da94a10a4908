import React from 'react'
import { useLocation, useNavigate } from 'react-router'
import HeaderSection from '../../components/common/Header'
import { Download, iconSize } from '../../components/common/Icon'
import PaginationComponent from '../../components/common/Pagination'
import Table from '../../components/common/Table'
import { routeUrl } from '../../configs/appConfig'
import title from '../../constants/title'
import type { Route } from './+types/download-list'

// eslint-disable-next-line no-empty-pattern
export function meta({}: Route.MetaArgs) {
  return [{ title: title.downloadList.title }]
}

const exampleData = [
  {
    id: 1,
    date1: 'yyyy/mm/dd hh:mm',
    date2: 'yyyy/mm/dd',
    title: '鈴木田中山田渡辺大野健康組合',
    status: '作成中' as const,
  },
  {
    id: 2,
    date1: 'yyyy/mm/dd hh:mm',
    date2: 'yyyy/mm/dd',
    title: '鈴木組合',
    status: '作成中' as const,
  },
  {
    id: 3,
    date1: 'yyyy/mm/dd hh:mm',
    date2: 'yyyy/mm/dd',
    title: '終木組合',
    status: 'エラー' as const,
  },
  {
    id: 4,
    date1: 'yyyy/mm/dd hh:mm',
    date2: 'yyyy/mm/dd',
    title: '終木組合',
    status: 'エラー' as const,
  },
  {
    id: 5,
    date1: 'yyyy/mm/dd hh:mm',
    date2: 'yyyy/mm/dd',
    title: '終木組合',
    status: '完了' as const,
    downloadUrl: '#',
  },
  {
    id: 6,
    date1: 'yyyy/mm/dd hh:mm',
    date2: 'yyyy/mm/dd',
    title: '終木組合',
    status: '完了' as const,
    downloadUrl: '#',
  },
  {
    id: 7,
    date1: 'yyyy/mm/dd hh:mm',
    date2: 'yyyy/mm/dd',
    title: '終木組合',
    status: '完了' as const,
    downloadUrl: '#',
  },
  {
    id: 8,
    date1: 'yyyy/mm/dd hh:mm',
    date2: 'yyyy/mm/dd',
    title: '終木組合',
    status: '完了' as const,
    downloadUrl: '#',
  },
  {
    id: 9,
    date1: 'yyyy/mm/dd hh:mm',
    date2: 'yyyy/mm/dd',
    title: '終木組合',
    status: '完了' as const,
    downloadUrl: '#',
  },
  {
    id: 10,
    date1: 'yyyy/mm/dd hh:mm',
    date2: 'yyyy/mm/dd',
    title: '終木組合',
    status: '完了' as const,
    downloadUrl: '#',
  },
  {
    id: 11,
    date1: 'yyyy/mm/dd hh:mm',
    date2: 'yyyy/mm/dd',
    title: '終木組合',
    status: '完了' as const,
    downloadUrl: '#',
  },
  {
    id: 12,
    date1: 'yyyy/mm/dd hh:mm',
    date2: 'yyyy/mm/dd',
    title: '終木組合',
    status: '完了' as const,
    downloadUrl: '#',
  },
  {
    id: 13,
    date1: 'yyyy/mm/dd hh:mm',
    date2: 'yyyy/mm/dd',
    title: '終木組合',
    status: '完了' as const,
    downloadUrl: '#',
  },
  {
    id: 14,
    date1: 'yyyy/mm/dd hh:mm',
    date2: 'yyyy/mm/dd',
    title: '終木組合',
    status: '完了' as const,
    downloadUrl: '#',
  },
  {
    id: 15,
    date1: 'yyyy/mm/dd hh:mm',
    date2: 'yyyy/mm/dd',
    title: '山田組合',
    status: '完了' as const,
    downloadUrl: '#',
  },
  {
    id: 16,
    date1: 'yyyy/mm/dd hh:mm',
    date2: 'yyyy/mm/dd',
    title: '山田組合',
    status: '完了' as const,
    downloadUrl: '#',
  },
  {
    id: 17,
    date1: 'yyyy/mm/dd hh:mm',
    date2: 'yyyy/mm/dd',
    title: '山田組合',
    status: '完了' as const,
    downloadUrl: '#',
  },
]

const DownloadList: React.FC = () => {
  const location = useLocation()
  const navigate = useNavigate()
  const searchParams = new URLSearchParams(location.search)
  const currentPage = parseInt(searchParams.get('page') || '1')
  const ITEMS_PER_PAGE = 12
  const startIndex = (currentPage - 1) * ITEMS_PER_PAGE
  const endIndex = startIndex + ITEMS_PER_PAGE
  const currentItems = exampleData.slice(startIndex, endIndex)

  const handlePageChange = (page: number) => {
    const newSearchParams = new URLSearchParams(location.search)
    newSearchParams.set('page', page.toString())
    navigate({ search: newSearchParams.toString() })
  }

  const handleDownload = (id: string | number) => {
    const item = exampleData.find((item) => item.id === id)
    if (item?.downloadUrl) {
      window.open(item.downloadUrl, '_blank')
    }
  }

  return (
    <>
      <HeaderSection
        breadcrumbLinks={[
          { label: title.home.title, to: '/' },
          {
            label: title.downloadList.title,
            to: routeUrl.DOWNLOAD_LIST.path,
            isCurrent: true,
          },
        ]}
        firstTitle={title.downloadList.title}
        icon={
          <Download
            fill={''}
            width={iconSize.medium}
            height={iconSize.medium}
          />
        }
      />
      <div className={'mt-6'}>
        <Table data={currentItems} onDownload={handleDownload} />
      </div>
      <PaginationComponent
        totalItems={exampleData.length}
        itemsPerPage={ITEMS_PER_PAGE}
        currentPage={currentPage}
        onPageChange={handlePageChange}
      />
    </>
  )
}

export default DownloadList
