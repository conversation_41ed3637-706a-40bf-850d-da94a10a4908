import React from 'react'
import HeaderSection from '../../components/common/Header'
import { iconSize, ReadinessScore } from '../../components/common/Icon'
import { routeUrl } from '../../configs/appConfig'
import title from '../../constants/title'

type HealthCareManagementHeaderProps = {
  secondLabel: string
}

const HealthCareManagementHeader: React.FC<HealthCareManagementHeaderProps> = ({
  secondLabel,
}) => {
  return (
    <HeaderSection
      breadcrumbLinks={[
        { label: title.home.title, to: '/' },
        {
          label: title.healthCareManagement.title,
          to: routeUrl.HEALTH_CARE_MANAGEMENT.path,
        },
        { label: secondLabel, isCurrent: true },
      ]}
      firstTitle={title.healthCareManagement.title}
      secondTitle={secondLabel}
      icon={
        <ReadinessScore
          fill={''}
          width={iconSize.medium}
          height={iconSize.medium}
        />
      }
    />
  )
}

export default HealthCareManagementHeader
