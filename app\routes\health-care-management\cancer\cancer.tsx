import FilterByYear from '../../../components/common/FilterByYear'
import Tableau from '../../../components/common/TableauSection'
import TabSelector from '../../../components/common/TabSelector'
import { HEALTH_CARE_MANAGEMENT_CANCER_TAB } from '../../../constants'
import title from '../../../constants/title'
import HealthCareManagementHeader from '../HealthCareManagementHeader'

const HealthCareManagementCancer = () => {
  return (
    <>
      <HealthCareManagementHeader
        secondLabel={title.healthCareManagement.cancer.title}
      />
      <TabSelector tabs={HEALTH_CARE_MANAGEMENT_CANCER_TAB} />
      <FilterByYear notHasAgeGroup={true} />
      <Tableau />
    </>
  )
}

export default HealthCareManagementCancer
