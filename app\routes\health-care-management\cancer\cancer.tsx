import FilterByYear from '../../../components/common/FilterByYear'
import Tableau from '../../../components/common/TableauSection'
import TabSelector from '../../../components/common/TabSelector'
import { HEALTH_CARE_MANAGEMENT_CANCER_TAB } from '../../../constants'
import title from '../../../constants/title'
import { concatenateTitle } from '../../../helper/helper'
import HealthCareManagementHeader from '../HealthCareManagementHeader'
import type { Route } from './+types/cancer'

// eslint-disable-next-line no-empty-pattern
export function meta({}: Route.MetaArgs) {
  return [
    {
      title: concatenateTitle(
        title.healthCareManagement.title,
        title.healthCareManagement.cancer.title,
        title.healthCareManagement.cancer.child.cancelExamination,
      ),
    },
  ]
}

const HealthCareManagementCancer = () => {
  return (
    <>
      <HealthCareManagementHeader
        secondLabel={title.healthCareManagement.cancer.title}
      />
      <TabSelector tabs={HEALTH_CARE_MANAGEMENT_CANCER_TAB} />
      <FilterByYear notHasAgeGroup={true} />
      <Tableau />
    </>
  )
}

export default HealthCareManagementCancer
