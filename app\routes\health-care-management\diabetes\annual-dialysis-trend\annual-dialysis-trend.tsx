import TableauPageBase from '../../../../components/common/TableauPageBase'
import { TABLEAU_LINKS } from '../../../../constants/tableauLinks'
import title from '../../../../constants/title'
import { concatenateTitle } from '../../../../helper/helper'
import type { Route } from './+types/annual-dialysis-trend'

// eslint-disable-next-line no-empty-pattern
export function meta({}: Route.MetaArgs) {
  return [
    {
      title: concatenateTitle(
        title.healthCareManagement.title,
        title.healthCareManagement.diabetes.title,
        title.healthCareManagement.diabetes.child.annualDialysisTrend,
      ),
    },
  ]
}

const AnnualDialysisTrend: React.FC = () => {
  return (
    <TableauPageBase
      showHeader={true}
      showTabs={true}
      filterByYearProps={{
        notHasAgeGroup: true,
        hasMember: true,
      }}
      tableauWebProps={{
        src: TABLEAU_LINKS.ANNUAL_DIALYSIS_TREND,
      }}
    />
  )
}

export default AnnualDialysisTrend
