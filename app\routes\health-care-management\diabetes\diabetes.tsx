import { Outlet } from 'react-router-dom'
import TabSelector from '../../../components/common/TabSelector'
import { DIABETES_TAB } from '../../../constants'
import title from '../../../constants/title'
import HealthCareManagementHeader from '../HealthCareManagementHeader'

const HealthCareManagementDiabetes = () => {
  return (
    <>
      <HealthCareManagementHeader
        secondLabel={title.healthCareManagement.diabetes.title}
      />
      <TabSelector tabs={DIABETES_TAB} />
      <Outlet />
    </>
  )
}

export default HealthCareManagementDiabetes
