import TableauPageBase from '../../../../components/common/TableauPageBase'
import { TABLEAU_LINKS } from '../../../../constants/tableauLinks'
import title from '../../../../constants/title'
import { concatenateTitle } from '../../../../helper/helper'
import type { Route } from './+types/kidney-risk-count'

// eslint-disable-next-line no-empty-pattern
export function meta({}: Route.MetaArgs) {
  return [
    {
      title: concatenateTitle(
        title.healthCareManagement.title,
        title.healthCareManagement.diabetes.title,
        title.healthCareManagement.diabetes.child.kidneyRiskCount,
      ),
    },
  ]
}

const KidneyRiskCount: React.FC = () => {
  return (
    <TableauPageBase
      showHeader={true}
      showTabs={true}
      filterByYearProps={{
        notHasAgeGroup: true,
        hasMember: true,
      }}
      tableauWebProps={{
        src: TABLEAU_LINKS.KIDNEY_RISK_COUNT,
      }}
    />
  )
}

export default KidneyRiskCount
