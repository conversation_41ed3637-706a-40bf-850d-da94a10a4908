import { useEffect, useState } from 'react'
import { Link } from 'react-router-dom'
import HeaderSection from '../../components/common/Header'
import {
  ExpandCircleRight,
  iconSize,
  ReadinessScore,
} from '../../components/common/Icon'
import Tableau from '../../components/common/TableauSection'
import TrendDisplay from '../../components/common/Trendisplay'
import { routeUrl } from '../../configs/appConfig'
import {
  TABLEAU_LINKS,
  TABLEAU_STORAGE_KEYS,
} from '../../constants/tableauLinks'
import title from '../../constants/title'
import { formatNumber } from '../../helper/helper'
import type { Route } from './+types/health-care-management'

// eslint-disable-next-line no-empty-pattern
export function meta({}: Route.MetaArgs) {
  return [{ title: title.healthCareManagement.title }]
}

const HealthCareManagement: React.FC = () => {
  const [dataSheet, setDataSheet] = useState<any[]>([])
  const handleData = (data: any[]) => {
    setDataSheet(data)
  }

  useEffect(() => {
    const fetchDataFromLocalStorage = () => {
      const cachedData = localStorage.getItem(
        TABLEAU_STORAGE_KEYS.HEALTH_CARE_MANAGEMENT_DATA,
      )
      if (cachedData) {
        setDataSheet(JSON.parse(cachedData))
      }
    }
    fetchDataFromLocalStorage()
  }, [])

  return (
    <div>
      <HeaderSection
        breadcrumbLinks={[
          { label: title.home.title, to: '/' },
          { label: title.healthCareManagement.title, isCurrent: true },
        ]}
        firstTitle={title.healthCareManagement.title}
        icon={
          <ReadinessScore
            fill={''}
            width={iconSize.medium}
            height={iconSize.medium}
          />
        }
      />

      <p className={'mt-2 text-sm text-gray-900'}>
        {title.healthCareManagement.description1}
      </p>

      <div className={'mt-4 bg-green-50 p-8'}>
        <div className={'flex gap-4'}>
          <Link
            to={`${routeUrl.HEALTH_CARE_MANAGEMENT.children.SPECIFIC_HEALTH_CHECK.path}/${routeUrl.HEALTH_CARE_MANAGEMENT.children.SPECIFIC_HEALTH_CHECK.children.SPECIFIC_HEALTH_CHECK_RATE}`}
            className={'group block'}
          >
            <div
              className={
                'group h-[132px] w-[334px] cursor-pointer justify-center rounded-2xl border border-gray-100 bg-white p-4 shadow-base-2 hover:border-2 hover:border-green-600 hover:text-green-600'
              }
            >
              <div className={'flex items-center justify-center gap-2'}>
                <span
                  className={
                    'cursor-pointer text-sm-bold text-gray-900 group-hover:text-green-600'
                  }
                >
                  {title.healthCareManagement.specificHealthCheckRate}
                </span>
                <ExpandCircleRight />
              </div>
              <div className={'text-right'}>
                <TrendDisplay
                  currentValue={formatNumber(dataSheet?.[5]?.[1]?._value, true)}
                  trendValue={formatNumber(dataSheet?.[4]?.[1]?._value, true)}
                  previousValue={formatNumber(
                    dataSheet?.[3]?.[1]?._value,
                    true,
                  )}
                  unit={'%'}
                  isNegativeTrend={false}
                  isCenter={true}
                />
              </div>
            </div>
          </Link>
          <Tableau
            isHidden={true}
            data={handleData}
            src={TABLEAU_LINKS.HEALTH_CARE_MANAGEMENT}
            isData={true}
            storageKey={TABLEAU_STORAGE_KEYS.HEALTH_CARE_MANAGEMENT_DATA}
          />
          <Link
            to={`${routeUrl.HEALTH_CARE_MANAGEMENT.children.SPECIFIC_HEALTH_CHECK.path}/${routeUrl.HEALTH_CARE_MANAGEMENT.children.SPECIFIC_HEALTH_CHECK.children.OBESITY_REDUCTION_RATE}`}
            className={'group block'}
          >
            <div
              className={
                'group h-[132px] w-[334px] cursor-pointer rounded-2xl border border-gray-100 bg-white p-4 shadow-base-2 hover:border-2 hover:border-green-600 hover:text-green-600'
              }
            >
              <div className={'flex items-center justify-center gap-2'}>
                <span
                  className={
                    'cursor-pointer text-sm-bold text-gray-900 group-hover:text-green-600'
                  }
                >
                  {title.healthCareManagement.obesityReductionRate}
                </span>
                <ExpandCircleRight />
              </div>
              <div className={'text-right'}>
                <TrendDisplay
                  currentValue={formatNumber(dataSheet?.[2]?.[1]?._value, true)}
                  trendValue={formatNumber(dataSheet?.[1]?.[1]?._value, true)}
                  previousValue={formatNumber(
                    dataSheet?.[0]?.[1]?._value,
                    true,
                  )}
                  unit={'%'}
                  isNegativeTrend={false}
                  isCenter={true}
                />
              </div>
            </div>
          </Link>
        </div>
      </div>
    </div>
  )
}

export default HealthCareManagement
