import TableauPageBase from '../../../../components/common/TableauPageBase'
import { TABLEAU_LINKS } from '../../../../constants/tableauLinks'
import title from '../../../../constants/title'
import { concatenateTitle } from '../../../../helper/helper'
import type { Route } from './+types/diabetes-uncontrolled-patients'

// eslint-disable-next-line no-empty-pattern
export function meta({}: Route.MetaArgs) {
  return [
    {
      title: concatenateTitle(
        title.healthCareManagement.title,
        title.healthCareManagement.recommendedHealthCheck.title,
        title.healthCareManagement.recommendedHealthCheck.child
          .diabetesUncontrolledPatients,
      ),
    },
  ]
}

const DiabetesUncontrolledPatients: React.FC = () => {
  return (
    <TableauPageBase
      showHeader={true}
      showTabs={true}
      filterByYearProps={{
        notHasAgeGroup: true,
        hasMember: true,
      }}
      tableauWebProps={{
        src: TABLEAU_LINKS.DIABETES_UNCONTROLLED_PATIENTS,
      }}
    />
  )
}

export default DiabetesUncontrolledPatients
