import { Outlet } from 'react-router'
import TabSelector from '../../../components/common/TabSelector'
import { RECOMMENDED_HEALTH_CHECK_TAB } from '../../../constants'
import title from '../../../constants/title'
import HealthCareManagementHeader from '../HealthCareManagementHeader'

const RecommendedHealthCheck = () => {
  return (
    <>
      <HealthCareManagementHeader
        secondLabel={title.healthCareManagement.recommendedHealthCheck.title}
      />
      <TabSelector tabs={RECOMMENDED_HEALTH_CHECK_TAB} />
      <Outlet />
    </>
  )
}

export default RecommendedHealthCheck
