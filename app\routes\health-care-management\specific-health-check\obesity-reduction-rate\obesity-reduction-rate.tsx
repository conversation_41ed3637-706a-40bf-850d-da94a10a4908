import TableauPageBase from '../../../../components/common/TableauPageBase'
import { TABLEAU_LINKS } from '../../../../constants/tableauLinks'
import title from '../../../../constants/title'
import { concatenateTitle } from '../../../../helper/helper'
import type { Route } from './+types/obesity-reduction-rate'

// eslint-disable-next-line no-empty-pattern
export function meta({}: Route.MetaArgs) {
  return [
    {
      title: concatenateTitle(
        title.healthCareManagement.title,
        title.healthCareManagement.specificHealthCheck.title,
        title.healthCareManagement.specificHealthCheck.child
          .obesityReductionRate,
      ),
    },
  ]
}

const ObesityReductionRate: React.FC = () => {
  return (
    <TableauPageBase
      showHeader={true}
      showTabs={true}
      filterByYearProps={{
        notHasAgeGroup: true,
        hasMember: true,
      }}
      tableauWebProps={{
        src: TABLEAU_LINKS.OBESITY_REDUCTION_RATE,
      }}
    />
  )
}

export default ObesityReductionRate
