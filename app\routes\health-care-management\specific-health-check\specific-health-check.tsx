import { Outlet } from 'react-router'
import TabSelector from '../../../components/common/TabSelector'
import { SPECIFIC_HEALTH_CHECK_TAB } from '../../../constants'
import title from '../../../constants/title'
import HealthCareManagementHeader from '../HealthCareManagementHeader'

const SpecificHealthCheck = () => {
  return (
    <>
      <HealthCareManagementHeader
        secondLabel={title.healthCareManagement.specificHealthCheck.title}
      />
      <TabSelector tabs={SPECIFIC_HEALTH_CHECK_TAB} />
      <Outlet />
    </>
  )
}

export default SpecificHealthCheck
