import TableauPageBase from '../../../../components/common/TableauPageBase'
import { TABLEAU_LINKS } from '../../../../constants/tableauLinks'
import title from '../../../../constants/title'
import { concatenateTitle } from '../../../../helper/helper'
import type { Route } from './+types/target-population-rate'

// eslint-disable-next-line no-empty-pattern
export function meta({}: Route.MetaArgs) {
  return [
    {
      title: concatenateTitle(
        title.healthCareManagement.title,
        title.healthCareManagement.specificHealthCheck.title,
        title.healthCareManagement.specificHealthCheck.child
          .targetPopulationRate,
      ),
    },
  ]
}

const TargetPopulationRate: React.FC = () => {
  return (
    <>
      <TableauPageBase
        showHeader={true}
        showTabs={true}
        filterByYearProps={{
          hasMember: true,
          notHasAgeGroup: true,
        }}
        tableauWebProps={{
          src: TABLEAU_LINKS.SPECIFIC_TARGET,
        }}
      />
    </>
  )
}

export default TargetPopulationRate
