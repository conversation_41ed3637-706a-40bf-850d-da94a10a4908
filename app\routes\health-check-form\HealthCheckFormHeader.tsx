import React from 'react'
import HeaderSection from '../../components/common/Header'
import { iconSize, StethoscopeSearch } from '../../components/common/Icon'
import { routeUrl } from '../../configs/appConfig'
import title from '../../constants/title'

type HealthCheckFormHeaderProps = {
  secondLabel: string
  thirdLabel?: string
}

const HealthCheckFormHeader: React.FC<HealthCheckFormHeaderProps> = ({
  secondLabel,
  thirdLabel,
}) => {
  return (
    <HeaderSection
      breadcrumbLinks={[
        { label: title.home.title, to: '/' },
        {
          label: title.healthCheckForm.title,
          to: routeUrl.HEALTH_CHECK_FORM.path,
        },
        { label: secondLabel, isCurrent: true },
      ]}
      firstTitle={title.healthCheckForm.title}
      secondTitle={thirdLabel ?? secondLabel}
      icon={
        <StethoscopeSearch
          fill={''}
          width={iconSize.medium}
          height={iconSize.medium}
        />
      }
    />
  )
}

export default HealthCheckFormHeader
