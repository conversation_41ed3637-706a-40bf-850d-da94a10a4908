import TableauPageBase from '../../../components/common/TableauPageBase'
import { CHECKUP_RATE_TAB } from '../../../constants'
import { TABLEAU_LINKS } from '../../../constants/tableauLinks'
import title from '../../../constants/title'
import { concatenateTitle } from '../../../helper/helper'
import HealthCheckFormHeader from '../HealthCheckFormHeader'
import type { Route } from './+types/checkup-rate'

// eslint-disable-next-line no-empty-pattern
export function meta({}: Route.MetaArgs) {
  return [
    {
      title: concatenateTitle(
        title.healthCheckForm.title,
        title.healthCheckForm.checkupRate.title,
        title.healthCheckForm.checkupRate.child.healthCheckRate,
      ),
    },
  ]
}

const CheckupRate: React.FC = () => {
  return (
    <TableauPageBase
      showHeader={true}
      headerSecondLabel={title.healthCheckForm.checkupRate.title}
      HeaderComponent={HealthCheckFormHeader}
      showTabs={true}
      tabs={CHECKUP_RATE_TAB}
      filterByYearProps={{
        hasMember: true,
        hasFilterRadio: true,
      }}
      tableauWebProps={{
        src: TABLEAU_LINKS.CHECK_UP_RATE,
      }}
    />
  )
}

export default CheckupRate
