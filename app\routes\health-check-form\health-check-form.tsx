import { useEffect, useState } from 'react'
import { Link } from 'react-router-dom'
import HeaderSection from '../../components/common/Header'
import {
  ExpandCircleRight,
  iconSize,
  StethoscopeSearch,
} from '../../components/common/Icon'
import Tableau from '../../components/common/TableauSection'
import SegmentedControl from '../../components/common/ToggleButton'
import TrendDisplay from '../../components/common/Trendisplay'
import TableauWeb from '../../components/tableau/tableauWeb'
import { routeUrl } from '../../configs/appConfig'
import { PERIOD } from '../../constants'
import {
  TABLEAU_LINKS,
  TABLEAU_STORAGE_KEYS,
} from '../../constants/tableauLinks'
import title from '../../constants/title'
import { formatNumber, formatYearMonth } from '../../helper/helper'
import type { Route } from './+types/health-check-form'

// eslint-disable-next-line no-empty-pattern
export function meta({}: Route.MetaArgs) {
  return [{ title: title.healthCheckForm.title }]
}

const HealthCheckForm: React.FC = () => {
  const [dataSheet, setDataSheet] = useState<any[]>([])

  const handleData = (data: any[]) => {
    setDataSheet(data)
  }

  useEffect(() => {
    const fetchDataFromLocalStorage = () => {
      const cachedData = localStorage.getItem(
        TABLEAU_STORAGE_KEYS.HEALTH_CHECK_FORM_DATA,
      )
      if (cachedData) {
        setDataSheet(JSON.parse(cachedData))
      }
    }
    fetchDataFromLocalStorage()
  }, [])

  const [tableauSrc, setTableauSrc] = useState(TABLEAU_LINKS.HEALTH_CHECK_FORM)
  const handleSegmentChange = (value: string) => {
    if (value === PERIOD.MONTH) {
      setTableauSrc(TABLEAU_LINKS.HEALTH_CHECK_FORM)
    } else if (value === PERIOD.YEAR) {
      setTableauSrc(TABLEAU_LINKS.HEALTH_CHECK_FORM_YEAR)
    }
  }

  console.log('dataSheet', dataSheet)
  return (
    <div>
      <HeaderSection
        breadcrumbLinks={[
          { label: title.home.title, to: '/' },
          { label: title.healthCheckForm.title, isCurrent: true },
        ]}
        firstTitle={title.healthCheckForm.title}
        icon={
          <StethoscopeSearch
            fill={''}
            width={iconSize.medium}
            height={iconSize.medium}
          />
        }
      />

      <p className={'mt-2 text-sm text-gray-900'}>
        {title.healthCheckForm.description1}
      </p>
      <p className={'text-sm-bold text-gray-900'}>
        {title.healthCheckForm.description2}
      </p>

      <div className={'mt-4 rounded-lg bg-green-50 p-8 shadow-md'}>
        <div className={'grid h-[470px] grid-cols-12 gap-8'}>
          <div className={'col-span-7 space-y-4 '}>
            <div className={'rounded-2xl border border-gray-100 bg-white p-4'}>
              <div className={'col-span-12 flex items-center justify-between'}>
                <Link
                  to={routeUrl.HEALTH_CHECK_FORM.children.CHECKUP_RATE.path}
                  className={
                    'group flex flex-1 items-center gap-2 hover:text-green-600'
                  }
                >
                  <div className={'flex items-center gap-2'}>
                    <span
                      className={
                        'text-sm-bold text-gray-900 group-hover:text-green-600'
                      }
                    >
                      {title.healthCheckForm.examineeTrend}
                    </span>
                    <ExpandCircleRight />
                  </div>
                </Link>
                <button className={'text-blue-500'}>
                  <SegmentedControl
                    options={[
                      { label: '月', value: PERIOD.MONTH },
                      { label: '年度', value: PERIOD.YEAR },
                    ]}
                    onChange={handleSegmentChange}
                  />
                </button>
              </div>
              <div
                className={'mt-4 flex h-[256px] justify-center overflow-hidden'}
              >
                <TableauWeb isSummary={true} src={tableauSrc} />
              </div>
              <Tableau
                isHidden={true}
                data={handleData}
                src={TABLEAU_LINKS.HEALTH_CHECK_FORM_DATA}
                isData={true}
                storageKey={TABLEAU_STORAGE_KEYS.HEALTH_CHECK_FORM_DATA}
              />
            </div>
            <Link
              to={routeUrl.HEALTH_CHECK_FORM.children.CHECKUP_RATE.path}
              className={'group block'}
            >
              <div
                className={
                  'cursor-pointer rounded-2xl border border-gray-100 bg-white p-4 shadow-md hover:border-2 hover:border-green-600 hover:text-green-600'
                }
              >
                <div className={'flex items-center gap-2'}>
                  <span
                    className={
                      'text-sm-bold text-gray-900 group-hover:text-green-600'
                    }
                  >
                    {title.healthCheckForm.examineeCount}
                  </span>
                  <ExpandCircleRight />
                </div>
                <div className={'text-right'}>
                  <TrendDisplay
                    currentValue={formatNumber(dataSheet[11]?.[1]?._value)}
                    trendValue={formatNumber(dataSheet[10]?.[1]?._value)}
                    previousValue={formatNumber(dataSheet[9]?.[1]?._value)}
                    unit={'人'}
                    isNegativeTrend={false}
                  />
                </div>
              </div>
            </Link>
          </div>

          <div className={'col-span-5'}>
            <Link
              to={routeUrl.HEALTH_CHECK_FORM.children.CHECKUP_RATE.path}
              className={'group block'}
            >
              <div
                className={
                  'group h-[138px] cursor-pointer rounded-2xl border border-gray-100 bg-white p-4 shadow-md hover:border-2 hover:border-green-600 hover:text-green-600'
                }
              >
                <div className={'flex items-center gap-2'}>
                  <span
                    className={
                      'cursor-pointer text-sm-bold text-gray-900 group-hover:text-green-600'
                    }
                  >
                    {title.healthCheckForm.examinationRate}
                  </span>
                  <ExpandCircleRight />
                </div>
                <div className={'text-right'}>
                  <TrendDisplay
                    currentValue={formatNumber(dataSheet[8]?.[1]?._value, true)}
                    trendValue={formatNumber(dataSheet[7]?.[1]?._value, true)}
                    previousValue={formatNumber(
                      dataSheet[6]?.[1]?._value,
                      true,
                    )}
                    unit={'%'}
                    isNegativeTrend={false}
                  />
                </div>
              </div>
            </Link>
            <Link
              to={routeUrl.HEALTH_CHECK_FORM.children.METABOLIC_SYNDROME.path}
              className={'group block'}
            >
              <div
                className={
                  'group mt-4 h-[138px] cursor-pointer rounded-2xl border border-gray-100 bg-white p-4 shadow-md hover:border-2 hover:border-green-600 hover:text-green-600'
                }
              >
                <div className={'flex items-center gap-2'}>
                  <span
                    className={
                      'cursor-pointer text-sm-bold text-gray-900 group-hover:text-green-600'
                    }
                  >
                    {title.healthCheckForm.metabolicSyndromeCount}
                  </span>
                  <ExpandCircleRight />
                </div>
                <div className={'text-right'}>
                  <TrendDisplay
                    currentValue={formatNumber(dataSheet[5]?.[1]?._value)}
                    trendValue={formatNumber(dataSheet[4]?.[1]?._value)}
                    previousValue={formatNumber(dataSheet[3]?.[1]?._value)}
                    unit={'人'}
                  />
                </div>
              </div>
            </Link>
            <Link
              to={routeUrl.HEALTH_CHECK_FORM.children.METABOLIC_SYNDROME.path}
              className={'group block'}
            >
              <div
                className={
                  'group mt-4 h-[138px] cursor-pointer rounded-2xl border border-gray-100 bg-white p-4 shadow-md hover:border-2 hover:border-green-600 hover:text-green-600'
                }
              >
                <div className={'flex items-center gap-2'}>
                  <span
                    className={
                      'cursor-pointer text-sm-bold text-gray-900 group-hover:text-green-600'
                    }
                  >
                    {title.healthCheckForm.metabolicSyndromeRate}
                  </span>
                  <ExpandCircleRight />
                </div>
                <div className={'text-right'}>
                  <TrendDisplay
                    currentValue={formatNumber(dataSheet[2]?.[1]?._value, true)}
                    trendValue={formatNumber(dataSheet[1]?.[1]?._value, true)}
                    previousValue={formatNumber(
                      dataSheet[0]?.[1]?._value,
                      true,
                    )}
                    unit={'％'}
                  />
                </div>
              </div>
            </Link>
            <div className={'mt-2 text-right text-xs text-gray-900'}>
              ※集計期間：前年度 {formatYearMonth(dataSheet?.[12]?.[1]?._value)}-
              {formatYearMonth(dataSheet?.[13]?.[1]?._value)}、今年度{' '}
              {formatYearMonth(dataSheet?.[14]?.[1]?._value)}-
              {formatYearMonth(dataSheet?.[15]?.[1]?._value)}{' '}
            </div>
          </div>
        </div>
      </div>

      <div className={'mt-4 rounded-lg bg-green-50 p-8 shadow-md'}>
        <div className={'flex items-center justify-between'}>
          <p className={'text-base-bold text-gray-600'}>{title.itemAnalysis}</p>
        </div>
        <div
          className={
            'mt-2 grid grid-cols-4 items-center gap-4 text-center text-base-bold text-gray-900'
          }
        >
          <Link
            to={routeUrl.HEALTH_CHECK_FORM.children.CHECKUP_RATE.path}
            className={'group block'}
          >
            <div
              className={
                'flex h-[60px] cursor-pointer items-center justify-center rounded-2xl bg-white shadow-md hover:border-2 hover:border-green-600 hover:text-green-600'
              }
            >
              {title.healthCheckForm.checkupRate.title}
            </div>
          </Link>
          <Link
            to={routeUrl.HEALTH_CHECK_FORM.children.ITEM_DETAILS.path}
            className={'group block'}
          >
            <div
              className={
                'flex h-[60px] cursor-pointer items-center justify-center rounded-2xl bg-white shadow-md hover:border-2 hover:border-green-600 hover:text-green-600'
              }
            >
              {title.healthCheckForm.itemDetails.title}
            </div>
          </Link>
          <Link
            to={routeUrl.HEALTH_CHECK_FORM.children.OUT_OF_STANDARD.path}
            className={'group block'}
          >
            <div
              className={
                'flex h-[60px] cursor-pointer items-center justify-center rounded-2xl bg-white shadow-md hover:border-2 hover:border-green-600 hover:text-green-600'
              }
            >
              {title.healthCheckForm.outOfStandard.title}
            </div>
          </Link>
          <Link
            to={routeUrl.HEALTH_CHECK_FORM.children.METABOLIC_SYNDROME.path}
            className={'group block'}
          >
            <div
              className={
                'flex h-[60px] cursor-pointer items-center justify-center rounded-2xl bg-white shadow-md hover:border-2 hover:border-green-600 hover:text-green-600'
              }
            >
              {title.healthCheckForm.metabolicSyndrome.title}
            </div>
          </Link>
          <Link
            to={routeUrl.HEALTH_CHECK_FORM.children.HEALTH_ISSUE_MAP.path}
            className={'group block'}
          >
            <div
              className={
                'flex h-[60px] cursor-pointer items-center justify-center rounded-2xl bg-white shadow-md hover:border-2 hover:border-green-600 hover:text-green-600'
              }
            >
              {title.healthCheckForm.healthIssueMap.title}
            </div>
          </Link>
        </div>
      </div>
    </div>
  )
}

export default HealthCheckForm
