import DiabetesStatus from '../../../components/common/DiebetesStatus'
import TableauPageBase from '../../../components/common/TableauPageBase'
import { HEALTH_ISSUE_MAP_TAB } from '../../../constants'
import { TABLEAU_LINKS } from '../../../constants/tableauLinks'
import title from '../../../constants/title'
import { concatenateTitle } from '../../../helper/helper'
import HealthCheckFormHeader from '../HealthCheckFormHeader'
import type { Route } from './+types/health-issue-map'

// eslint-disable-next-line no-empty-pattern
export function meta({}: Route.MetaArgs) {
  return [
    {
      title: concatenateTitle(
        title.healthCheckForm.title,
        title.healthCheckForm.healthIssueMap.title,
      ),
    },
  ]
}

const HealthIssueMap: React.FC = () => {
  return (
    <TableauPageBase
      showHeader={true}
      headerSecondLabel={title.healthCheckForm.healthIssueMap.title}
      HeaderComponent={HealthCheckFormHeader}
      showTabs={true}
      additionalComponents={[<DiabetesStatus key={'diabetes-status'} />]}
      tabs={HEALTH_ISSUE_MAP_TAB}
      filterByYearProps={{
        hasMember: true,
      }}
      tableauWebProps={{
        src: TABLEAU_LINKS.HEALTH_ISSUE_MAP,
      }}
    />
  )
}

export default HealthIssueMap
