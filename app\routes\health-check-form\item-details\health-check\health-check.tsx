import TableauPageBase from '../../../../components/common/TableauPageBase'
import { TABLEAU_LINKS } from '../../../../constants/tableauLinks'
import title from '../../../../constants/title'
import { concatenateTitle } from '../../../../helper/helper'
import type { Route } from './+types/health-check'

// eslint-disable-next-line no-empty-pattern
export function meta({}: Route.MetaArgs) {
  return [
    {
      title: concatenateTitle(
        title.healthCheckForm.title,
        title.healthCheckForm.itemDetails.title,
        title.healthCheckForm.itemDetails.child.healthCheck,
      ),
    },
  ]
}

const HealthIssueMap: React.FC = () => {
  return (
    <TableauPageBase
      showHeader={true}
      filterByYearProps={{
        hasMember: true,
      }}
      tableauWebProps={{
        src: TABLEAU_LINKS.HEALTH_CHECK,
      }}
    />
  )
}

export default HealthIssueMap
