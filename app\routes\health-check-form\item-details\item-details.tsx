import { Outlet } from 'react-router'
import TabSelector from '../../../components/common/TabSelector'
import { ITEM_DETAILS_TAB } from '../../../constants'
import title from '../../../constants/title'
import HealthCheckFormHeader from '../HealthCheckFormHeader'

const ItemDetails = () => {
  return (
    <>
      <HealthCheckFormHeader
        secondLabel={title.healthCheckForm.itemDetails.title}
      />
      <TabSelector tabs={ITEM_DETAILS_TAB} />
      <Outlet />
    </>
  )
}

export default ItemDetails
