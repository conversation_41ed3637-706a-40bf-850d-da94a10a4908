import TableauPageBase from '../../../../components/common/TableauPageBase'
import { EXPORT_TYPES } from '../../../../constants'
import { TABLEAU_LINKS } from '../../../../constants/tableauLinks'

const Questionnaire: React.FC = () => {
  return (
    <TableauPageBase
      filterByYearProps={{
        hasMember: true,
        notHasAgeGroup: true,
        excludeExport: [EXPORT_TYPES.BID],
      }}
      tableauWebProps={{
        src: TABLEAU_LINKS.QUESTIONNAIRE,
      }}
    />
  )
}

export default Questionnaire
