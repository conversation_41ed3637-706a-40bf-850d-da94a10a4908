import type { Route } from '../+types/health-check-form'
import TableauPageBase from '../../../components/common/TableauPageBase'
import { METABOLIC_SYNDROME_TAB } from '../../../constants'
import { TABLEAU_LINKS } from '../../../constants/tableauLinks'
import title from '../../../constants/title'
import { concatenateTitle } from '../../../helper/helper'

// eslint-disable-next-line no-empty-pattern
export function meta({}: Route.MetaArgs) {
  return [
    {
      title: concatenateTitle(
        title.healthCheckForm.title,
        title.healthCheckForm.checkupRate.title,
        title.healthCheckForm.checkupRate.child.healthCheckRate,
      ),
    },
  ]
}

const MetabolicSyndrome: React.FC = () => {
  return (
    <TableauPageBase
      showHeader={true}
      headerSecondLabel={title.healthCheckForm.checkupRate.title}
      showTabs={true}
      tabs={METABOLIC_SYNDROME_TAB}
      filterByYearProps={{
        hasMember: true,
      }}
      tableauWebProps={{
        src: TABLEAU_LINKS.METABOLIC_SYNDROME,
      }}
    />
  )
}

export default MetabolicSyndrome
