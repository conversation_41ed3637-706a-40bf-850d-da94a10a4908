import TableauPageBase from '../../../../components/common/TableauPageBase'
import { TABLEAU_LINKS } from '../../../../constants/tableauLinks'
import title from '../../../../constants/title'
import { concatenateTitle } from '../../../../helper/helper'
import type { Route } from './+types/health-check-rate'

// eslint-disable-next-line no-empty-pattern
export function meta({}: Route.MetaArgs) {
  return [
    {
      title: concatenateTitle(
        title.healthCheckForm.title,
        title.healthCheckForm.outOfStandard.title,
        title.healthCheckForm.outOfStandard.child.healthCheckRate,
      ),
    },
  ]
}

const HealthCheckRate: React.FC = () => {
  return (
    <TableauPageBase
      showHeader={true}
      filterByYearProps={{
        hasMember: true,
      }}
      tableauWebProps={{
        src: TABLEAU_LINKS.HEALTH_CHECK_RATE,
      }}
    />
  )
}

export default HealthCheckRate
