import { Outlet } from 'react-router'
import TabSelector from '../../../components/common/TabSelector'
import { OUT_OF_STANDARD_TAB } from '../../../constants'
import title from '../../../constants/title'
import HealthCheckFormHeader from '../HealthCheckFormHeader'

const OutOfStandard = () => {
  return (
    <>
      <HealthCheckFormHeader
        secondLabel={title.healthCheckForm.outOfStandard.title}
      />
      <TabSelector tabs={OUT_OF_STANDARD_TAB} />
      <Outlet />
    </>
  )
}

export default OutOfStandard
