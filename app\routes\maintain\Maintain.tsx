import title from '../../constants/title'

const notices = [
  {
    id: 1,
    title: '障害のお知らせ',
    content:
      '8月12日（月）から8月19日(月)10:00（サービス停止措置以前）までの期間において、らくらく健助にて正しいデータ集計ができていない状況を検知しましたので、下記の通りご報告いたします。ご迷惑をおかけし誠に申し訳ございません。',
    createdAt: '2025/05/15',
  },
  {
    id: 2,
    title: '障害のお知らせ',
    content:
      '8月12日（月）から8月19日(月)10:00（サービス停止措置以前）までの期間において、らくらく健助にて正しいデータ集計ができていない状況を検知しましたので、下記の通りご報告いたします。ご迷惑をおかけし誠に申し訳ございません。',
    createdAt: '2025/05/15',
  },
  {
    id: 3,
    title: '障害のお知らせ',
    content:
      '8月12日（月）から8月19日(月)10:00（サービス停止措置以前）までの期間において、らくらく健助にて正しいデータ集計ができていない状況を検知しましたので、下記の通りご報告いたします。ご迷惑をおかけし誠に申し訳ございません。',
    createdAt: '2025/05/15',
  },
  {
    id: 4,
    title: '障害のお知らせ',
    content:
      '8月12日（月）から8月19日(月)10:00（サービス停止措置以前）までの期間において、らくらく健助にて正しいデータ集計ができていない状況を検知しましたので、下記の通りご報告いたします。ご迷惑をおかけし誠に申し訳ございません。',
    createdAt: '2025/05/15',
  },
  {
    id: 5,
    title: '障害のお知らせ',
    content:
      '8月12日（月）から8月19日(月)10:00（サービス停止措置以前）までの期間において、らくらく健助にて正しいデータ集計ができていない状況を検知しましたので、下記の通りご報告いたします。ご迷惑をおかけし誠に申し訳ございません。',
    createdAt: '2025/05/15',
  },
  {
    id: 6,
    title: '障害のお知らせ',
    content:
      '8月12日（月）から8月19日(月)10:00（サービス停止措置以前）までの期間において、らくらく健助にて正しいデータ集計ができていない状況を検知しましたので、下記の通りご報告いたします。ご迷惑をおかけし誠に申し訳ございません。',
    createdAt: '2025/05/15',
  },
]

const Maintain = () => {
  return (
    <div className={'flex min-h-[calc(100vh-96px)] flex-col bg-beige-400'}>
      <div
        className={
          'mx-auto mt-[73px] flex h-[120px] w-[400px] items-center justify-center bg-gray-100'
        }
      >
        <p className={'text-xl font-semibold text-gray-600'}>
          {title.home.title}
        </p>
      </div>

      <div className={'mx-auto mt-[64px] w-[880px]'}>
        <p className={'text-base font-semibold text-gray-900'}>
          {title.login.maintainTitle}
        </p>
        <div className={'mt-2 rounded-xl border border-gray-300 bg-white p-4'}>
          <div
            className={
              'flex h-[240px] w-full flex-col gap-4 overflow-y-auto pr-4'
            }
          >
            {notices.map((notice, index) => (
              <div key={notice.id}>
                <p className={'text-xs text-gray-600'}>{notice.createdAt}</p>
                <p className={'text-sm font-semibold text-gray-900'}>
                  {notice.title}
                </p>
                <p className={'text-sm text-gray-900'}>{notice.content}</p>
                {index < notices.length - 1 && (
                  <hr
                    className={'mt-4 border-t border-dotted border-gray-300'}
                  />
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
      <div className={'mt-[64px] flex flex-col items-center'}>
        <p className={'text-lg-bold text-red-700'}>
          {title.login.maintainDesc}
        </p>
      </div>
    </div>
  )
}

export default Maintain
