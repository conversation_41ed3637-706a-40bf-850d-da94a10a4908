import React from 'react'
import HeaderSection from '../../components/common/Header'
import { GroupSearch, iconSize } from '../../components/common/Icon'
import { routeUrl } from '../../configs/appConfig'
import title from '../../constants/title'

type MemberCountHeaderProps = {
  secondLabel: string
}

const MemberCountHeader: React.FC<MemberCountHeaderProps> = ({
  secondLabel,
}) => {
  return (
    <HeaderSection
      breadcrumbLinks={[
        { label: title.home.title, to: '/' },
        { label: title.memberInformation.title, to: routeUrl.MEMBER_INFO.path },
        { label: secondLabel, isCurrent: true },
      ]}
      firstTitle={title.memberInformation.title}
      secondTitle={secondLabel}
      icon={
        <GroupSearch
          fill={''}
          width={iconSize.medium}
          height={iconSize.medium}
        />
      }
    />
  )
}

export default MemberCountHeader
