import TableauPageBase from '../../../components/common/TableauPageBase'
import { AVERAGE_AGE_TAB, EXPORT_TYPES } from '../../../constants'
import { TABLEAU_LINKS } from '../../../constants/tableauLinks'
import title from '../../../constants/title'
import { concatenateTitle } from '../../../helper/helper'
import type { Route } from './+types/average-age'

// eslint-disable-next-line no-empty-pattern
export function meta({}: Route.MetaArgs) {
  return [
    {
      title: concatenateTitle(
        title.memberInformation.title,
        title.memberInformation.averageAge.title,
      ),
    },
  ]
}

const AverageAge: React.FC = () => {
  return (
    <TableauPageBase
      showHeader={true}
      headerSecondLabel={title.memberInformation.averageAge.title}
      showTabs={true}
      tabs={AVERAGE_AGE_TAB}
      filterByYearProps={{
        notHasAgeGroup: true,
        hasMember: true,
        excludeExport: [EXPORT_TYPES.CSV, EXPORT_TYPES.BID],
      }}
      tableauWebProps={{
        src: TABLEAU_LINKS.MEMBER_AVG_AGE,
      }}
    />
  )
}

export default AverageAge
