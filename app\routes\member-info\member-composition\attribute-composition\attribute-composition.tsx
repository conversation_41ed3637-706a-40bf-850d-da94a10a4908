import TableauPageBase from '../../../../components/common/TableauPageBase'
import FILTER from '../../../../constants/filter'
import { TABLEAU_LINKS } from '../../../../constants/tableauLinks'
import title from '../../../../constants/title'
import { concatenateTitle } from '../../../../helper/helper'
import type { Route } from './+types/attribute-composition'

// eslint-disable-next-line no-empty-pattern
export function meta({}: Route.MetaArgs) {
  return [
    {
      title: concatenateTitle(
        title.memberInformation.title,
        title.memberInformation.memberComposition.title,
        title.memberInformation.memberComposition.child.attributeComposition,
      ),
    },
  ]
}

const AttributeComposition: React.FC = () => {
  return (
    <TableauPageBase
      showHeader={false}
      showTabs={false}
      filterByYearProps={{
        notHasAgeGroup: true,
        notHasInsurance: true,
        hasMember: true,
      }}
      tableauWebProps={{
        src: TABLEAU_LINKS.MEMBER_ATTRIBUTE_COMPOSITION,
        filterKey: [FILTER.modal, FILTER.yearParameter, FILTER.member],
        markKey: [FILTER.insurance, FILTER.ageGroup],
        exportTitle:
          title.memberInformation.memberComposition.child.attributeComposition,
      }}
    />
  )
}

export default AttributeComposition
