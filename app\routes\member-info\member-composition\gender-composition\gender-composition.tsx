import TableauPageBase from '../../../../components/common/TableauPageBase'
import FILTER from '../../../../constants/filter'
import { TABLEAU_LINKS } from '../../../../constants/tableauLinks'
import title from '../../../../constants/title'
import { concatenateTitle } from '../../../../helper/helper'
import type { Route } from './+types/gender-composition'

// eslint-disable-next-line no-empty-pattern
export function meta({}: Route.MetaArgs) {
  return [
    {
      title: concatenateTitle(
        title.memberInformation.title,
        title.memberInformation.memberComposition.title,
        title.memberInformation.memberComposition.child.genderComposition,
      ),
    },
  ]
}

const GenderComposition: React.FC = () => {
  return (
    <TableauPageBase
      filterByYearProps={{
        notHasAgeGroup: true,
        hasMember: true,
      }}
      tableauWebProps={{
        src: TABLEAU_LINKS.MEMBER_GENDER_COMPOSITION,
        filterKey: [
          FILTER.modal,
          FILTER.yearParameter,
          FILTER.insurance,
          FILTER.member,
        ],
        mark<PERSON>ey: [FILTER.gender],
        exportTitle:
          title.memberInformation.memberComposition.child.genderComposition,
      }}
    />
  )
}

export default GenderComposition
