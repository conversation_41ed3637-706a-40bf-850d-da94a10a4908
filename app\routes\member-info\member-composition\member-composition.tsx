import { Outlet } from 'react-router'
import TabSelector from '../../../components/common/TabSelector'
import { MEMBER_COMPOSITION_TAB } from '../../../constants'
import title from '../../../constants/title'
import MemberInfoHeader from '../MemberInfoHeader'

const MemberComposition = () => {
  return (
    <>
      <MemberInfoHeader
        secondLabel={title.memberInformation.memberComposition.title}
      />
      <TabSelector tabs={MEMBER_COMPOSITION_TAB} />
      <Outlet />
    </>
  )
}

export default MemberComposition
