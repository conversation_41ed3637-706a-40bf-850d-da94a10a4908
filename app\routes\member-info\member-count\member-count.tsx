import TableauPageBase from '../../../components/common/TableauPageBase'
import { MEMBER_COUNT_TAB } from '../../../constants'
import FILTER from '../../../constants/filter'
import { TABLEAU_LINKS } from '../../../constants/tableauLinks'
import title from '../../../constants/title'
import { concatenateTitle } from '../../../helper/helper'
import type { Route } from './+types/member-count'

// eslint-disable-next-line no-empty-pattern
export function meta({}: Route.MetaArgs) {
  return [
    {
      title: concatenateTitle(
        title.memberInformation.title,
        title.memberInformation.memberCount.title,
        title.memberInformation.memberCount.child.subscriberNumber,
      ),
    },
  ]
}

const MemberCount: React.FC = () => {
  return (
    <TableauPageBase
      showHeader={true}
      headerSecondLabel={title.memberInformation.memberCount.title}
      showTabs={true}
      tabs={MEMBER_COUNT_TAB}
      filterByYearProps={{
        notHasInsurance: true,
        hasMember: true,
      }}
      tableauWebProps={{
        src: TABLEAU_LINKS.MEMBER_COUNT,
        filterKey: [
          FILTER.modal,
          FILTER.yearParameter,
          FILTER.ageGroup,
          FILTER.member,
        ],
        markKey: [FILTER.insurance, FILTER.fiscalYear],
        exportTitle: title.memberInformation.memberCount.child.subscriberNumber,
      }}
    />
  )
}

export default MemberCount
