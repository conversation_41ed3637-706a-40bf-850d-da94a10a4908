import { useEffect, useState } from 'react'
import { Link } from 'react-router-dom'
import HeaderSection from '../../components/common/Header'
import {
  ExpandCircleRight,
  GroupSearch,
  iconSize,
} from '../../components/common/Icon'
import Tableau from '../../components/common/TableauSection'
import SegmentedControl from '../../components/common/ToggleButton'
import TrendDisplay from '../../components/common/Trendisplay'
import TableauWeb from '../../components/tableau/tableauWeb'
import { routeUrl } from '../../configs/appConfig'
import {
  TABLEAU_LINKS,
  TABLEAU_STORAGE_KEYS,
} from '../../constants/tableauLinks'
import title from '../../constants/title'
import { formatNumber } from '../../helper/helper'

const MemberInfo: React.FC = () => {
  const [dataSheet, setDataSheet] = useState<any[]>([])
  const [selectedView, setSelectedView] = useState<'month' | 'year'>('month')
  const handleData = (data: any[]) => {
    setDataSheet(data)
  }

  useEffect(() => {
    const fetchDataFromLocalStorage = () => {
      const cachedData = localStorage.getItem(
        TABLEAU_STORAGE_KEYS.MEMBER_INFO_DATA,
      )
      if (cachedData) {
        setDataSheet(JSON.parse(cachedData))
      }
    }
    fetchDataFromLocalStorage()
  }, [])

  return (
    <div>
      <HeaderSection
        breadcrumbLinks={[
          { label: title.home.title, to: '/' },
          { label: title.memberInformation.title, isCurrent: true },
        ]}
        firstTitle={title.memberInformation.title}
        icon={
          <GroupSearch
            fill={''}
            width={iconSize.medium}
            height={iconSize.medium}
          />
        }
      />

      <p className={'mt-2 text-sm text-gray-900'}>
        {title.memberInformation.description1}
      </p>
      <p className={'text-sm-bold text-gray-900'}>
        {title.memberInformation.description2}
      </p>

      <div className={'mt-4 rounded-lg bg-green-50 p-8 shadow-md'}>
        <div className={'grid h-[470px] grid-cols-12 gap-8'}>
          <div className={'col-span-7 space-y-4 '}>
            <div className={'rounded-2xl border border-gray-100 bg-white p-4'}>
              <div className={'col-span-12 flex items-center justify-between'}>
                <Link
                  to={routeUrl.MEMBER_INFO.children.MEMBER_COUNT.path}
                  className={
                    'group flex flex-1 items-center gap-2 hover:text-green-600'
                  }
                >
                  <div className={'flex items-center gap-2'}>
                    <span
                      className={
                        'text-sm-bold text-gray-900 group-hover:text-green-600'
                      }
                    >
                      {title.memberInformation.memberCountTrend}
                    </span>
                    <ExpandCircleRight />
                  </div>
                </Link>
                <button className={'text-blue-500'}>
                  <SegmentedControl
                    options={[
                      { label: '月', value: 'month' },
                      { label: '年度', value: 'year' },
                    ]}
                    onChange={(value) =>
                      setSelectedView(value as 'month' | 'year')
                    }
                  />
                </button>
              </div>
              <div
                className={'mt-4 flex h-[256px] justify-center overflow-hidden'}
              >
                {selectedView === 'month' ?
                  <TableauWeb
                    isSummary={true}
                    src={TABLEAU_LINKS.MEMBER_INFO_MONTH}
                  />
                : <TableauWeb
                    isSummary={true}
                    src={TABLEAU_LINKS.MEMBER_INFO}
                  />
                }
              </div>
              <Tableau
                isHidden={true}
                data={handleData}
                src={TABLEAU_LINKS.MEMBER_DATA}
                isData={true}
                storageKey={TABLEAU_STORAGE_KEYS.MEMBER_INFO_DATA}
              />
            </div>
            <Link
              to={routeUrl.MEMBER_INFO.children.MEMBER_COUNT.path}
              className={'group block'}
            >
              <div
                className={
                  'cursor-pointer rounded-2xl border border-gray-100 bg-white p-4 shadow-md hover:border-2 hover:border-green-600 hover:text-green-600'
                }
              >
                <div className={'flex items-center gap-2'}>
                  <span
                    className={
                      'text-sm-bold text-gray-900 group-hover:text-green-600'
                    }
                  >
                    {title.memberInformation.memberCount.title}
                  </span>
                  <ExpandCircleRight />
                </div>
                <div className={'text-right'}>
                  <TrendDisplay
                    currentValue={formatNumber(dataSheet?.[8]?.[1]?._value)}
                    trendValue={formatNumber(dataSheet?.[7]?.[1]?._value)}
                    previousValue={formatNumber(dataSheet?.[6]?.[1]?._value)}
                    unit={'人'}
                    isNegativeTrend={false}
                  />
                </div>
              </div>
            </Link>
          </div>

          <div className={'col-span-5'}>
            <Link
              to={routeUrl.MEMBER_INFO.children.MEMBER_COMPOSITION.path}
              className={'group block'}
            >
              <div
                className={
                  'group h-[215px] cursor-pointer rounded-2xl border border-gray-100 bg-white p-4 shadow-md hover:border-2 hover:border-green-600 hover:text-green-600'
                }
              >
                <div className={'flex items-center gap-2'}>
                  <span
                    className={
                      'cursor-pointer text-sm-bold text-gray-900 group-hover:text-green-600'
                    }
                  >
                    {title.memberInformation.insuredMemberCount}
                  </span>
                  <ExpandCircleRight />
                </div>
                <div
                  className={
                    'flex h-[calc(215px-64px)] items-center justify-end'
                  }
                >
                  <div className={'text-right'}>
                    <TrendDisplay
                      currentValue={formatNumber(dataSheet?.[5]?.[1]?._value)}
                      trendValue={formatNumber(dataSheet?.[4]?.[1]?._value)}
                      previousValue={formatNumber(dataSheet?.[3]?.[1]?._value)}
                      unit={'人'}
                      isNegativeTrend={false}
                    />
                  </div>
                </div>
              </div>
            </Link>
            <Link
              to={routeUrl.MEMBER_INFO.children.AVERAGE_AGE.path}
              className={'group block'}
            >
              <div
                className={
                  'group mt-4 h-[215px] cursor-pointer rounded-2xl border border-gray-100 bg-white p-4 shadow-md hover:border-2 hover:border-green-600 hover:text-green-600'
                }
              >
                <div className={'flex items-center gap-2'}>
                  <span
                    className={
                      'cursor-pointer text-sm-bold text-gray-900 group-hover:text-green-600'
                    }
                  >
                    {title.memberInformation.insuredMemberAverageAge}
                  </span>
                  <ExpandCircleRight />
                </div>
                <div
                  className={
                    'flex h-[calc(215px-64px)] items-center justify-end'
                  }
                >
                  <div className={'text-right'}>
                    <TrendDisplay
                      currentValue={formatNumber(dataSheet?.[2]?.[1]?._value)}
                      trendValue={formatNumber(dataSheet?.[1]?.[1]?._value)}
                      previousValue={formatNumber(dataSheet?.[0]?.[1]?._value)}
                      unit={'歳'}
                    />
                  </div>
                </div>
              </div>
            </Link>

            <div className={'mt-2 text-right text-xs text-gray-900'}>
              ※集計期間：前年度 {dataSheet?.[9]?.[1]?._value}/
              {dataSheet?.[11]?.[1]?._value}-{dataSheet?.[10]?.[1]?._value}/
              {dataSheet?.[14]?.[1]?._value}、今年度{' '}
              {dataSheet?.[12]?.[1]?._value}/{dataSheet?.[14]?.[1]?._value}-
              {dataSheet?.[13]?.[1]?._value}/
              {dataSheet?.[13]?.[1]?._value <= 3 ?
                dataSheet?.[14]?.[1]?._value + 1
              : dataSheet?.[14]?.[1]?._value}
            </div>
          </div>
        </div>
      </div>

      <div className={'mt-4 rounded-lg bg-green-50 p-8 shadow-md'}>
        <div className={'flex items-center justify-between'}>
          <p className={'text-base-bold text-gray-600'}>{title.itemAnalysis}</p>
        </div>
        <div
          className={
            'mt-2 grid grid-cols-4 items-center gap-4 text-center text-base-bold text-gray-900'
          }
        >
          <Link
            to={`${routeUrl.MEMBER_INFO.path}/${routeUrl.MEMBER_INFO.children.MEMBER_COUNT.path}`}
            className={'group block'}
          >
            <div
              className={
                'flex h-[60px] cursor-pointer items-center justify-center rounded-2xl bg-white shadow-md hover:border-2 hover:border-green-600 hover:text-green-600'
              }
            >
              {title.memberInformation.memberCount.title}
            </div>
          </Link>
          <Link
            to={`${routeUrl.MEMBER_INFO.path}/${routeUrl.MEMBER_INFO.children.MEMBER_COMPOSITION.path}`}
            className={'group block'}
          >
            <div
              className={
                'flex h-[60px] cursor-pointer items-center justify-center rounded-2xl bg-white shadow-md hover:border-2 hover:border-green-600 hover:text-green-600'
              }
            >
              {title.memberInformation.memberComposition.title}
            </div>
          </Link>
          <Link
            to={`${routeUrl.MEMBER_INFO.path}/${routeUrl.MEMBER_INFO.children.AVERAGE_AGE.path}`}
            className={'group block'}
          >
            <div
              className={
                'flex h-[60px] cursor-pointer items-center justify-center rounded-2xl bg-white shadow-md hover:border-2 hover:border-green-600 hover:text-green-600'
              }
            >
              {title.memberInformation.averageAge.title}
            </div>
          </Link>
        </div>
      </div>
    </div>
  )
}

export default MemberInfo
