import { Link } from 'react-router'
import { ExpandCircleRight } from '../../components/common/Icon'
import Header from '../../components/layout/Header'
import title from '../../constants/title'
import type { Route } from './+types/not-found'

// eslint-disable-next-line no-empty-pattern
export function meta({}: Route.MetaArgs) {
  return [{ title: title.notFound.title }]
}

const NotFound = () => {
  return (
    <>
      <Header isShowMenu={true} isShowText={false} />
      <div className={'mt-40 flex flex-col items-center justify-center p-4'}>
        <img
          src={'/maintain-page/images/image404.png'}
          alt={'Quick Reference'}
        />
        <p className={'mt-8 text-base-bold text-gray-900'}>
          {title.notFound.description}
        </p>
        <div className={'mt-8 flex items-center  justify-center'}>
          <Link
            to={'/'}
            className={
              'flex h-[48px] w-[200px] items-center justify-center gap-2 rounded-xl text-lg text-green-600 underline hover:border-green-700 hover:text-green-700'
            }
          >
            {title.notFound.button}
            <ExpandCircleRight />
          </Link>
        </div>
      </div>
    </>
  )
}

export default NotFound
