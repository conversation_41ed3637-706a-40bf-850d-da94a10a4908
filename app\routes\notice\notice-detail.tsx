import { useEffect } from 'react'
import { FaBell } from 'react-icons/fa6'
import { Link, Navigate, useParams } from 'react-router'
import HeaderSection from '../../components/common/Header'
import { routeUrl } from '../../configs/appConfig'
import title from '../../constants/title'
import { NOTICE_LIST } from '../../example-data/notice'

const NoticeDetail: React.FC = () => {
  const { id } = useParams()
  const notify = NOTICE_LIST.find((item) => item.id === Number(id))

  useEffect(() => {
    if (notify) {
      document.title = notify.title
    }
  }, [notify])

  if (!notify) {
    return <Navigate to={routeUrl.HOME} />
  }

  return (
    <div className={'relative min-h-full'}>
      <div>
        <HeaderSection
          breadcrumbLinks={[
            { label: title.home.title, to: '/' },
            { label: title.notice.title, to: routeUrl.NOTICE.path },
            {
              label: title.notice.detail,
              to: `${routeUrl.NOTICE.path}/${id}`,
              isCurrent: true,
            },
          ]}
          firstTitle={title.notice.title}
          secondTitle={title.notice.detail}
          icon={<FaBell className={'size-6'} />}
        />

        <div className={'mt-6'}>
          <div className={'mb-4 border-b border-dashed pb-2'}>
            <div className={'mb-1 text-xs text-gray-600'}>{notify.date}</div>
            <div className={'text-base-bold text-gray-900'}>{notify.title}</div>
          </div>
        </div>

        <div className={'ml-4 mt-10'}>
          <div dangerouslySetInnerHTML={{ __html: notify.content }} />
        </div>
      </div>

      <div
        className={
          'absolute bottom-4 right-0 flex items-end justify-center text-base text-green-600 underline'
        }
        style={{
          left: '1rem',
        }}
      >
        <Link to={'/notices'}>{title.notice.backToList}</Link>
      </div>
    </div>
  )
}

export default NoticeDetail
