import React, { useState } from 'react'
import { FaBell } from 'react-icons/fa'
import { Link, useLocation, useNavigate } from 'react-router'
import HeaderSection from '../../components/common/Header'
import PaginationComponent from '../../components/common/Pagination'
import { routeUrl } from '../../configs/appConfig'
import title from '../../constants/title'
import { NOTICE_LIST } from '../../example-data/notice'
import type { Route } from './+types/notice'

// eslint-disable-next-line no-empty-pattern
export function meta({}: Route.MetaArgs) {
  return [{ title: title.notice.title }]
}

const Notice: React.FC = () => {
  const location = useLocation()
  const navigate = useNavigate()
  const searchParams = new URLSearchParams(location.search)
  const [currentPage, setCurrentPage] = useState(
    parseInt(searchParams.get('page') || '1'),
  )

  const ITEMS_PER_PAGE = 10
  const startIndex = (currentPage - 1) * ITEMS_PER_PAGE
  const endIndex = startIndex + ITEMS_PER_PAGE
  const currentItems = NOTICE_LIST.slice(startIndex, endIndex)

  const handlePageChange = (page: number) => {
    const newSearchParams = new URLSearchParams(location.search)
    newSearchParams.set('page', page.toString())
    navigate({ search: newSearchParams.toString() })
    setCurrentPage(page)
  }

  return (
    <>
      <HeaderSection
        breadcrumbLinks={[
          { label: title.home.title, to: '/' },
          {
            label: title.notice.title,
            to: routeUrl.NOTICE.path,
            isCurrent: true,
          },
        ]}
        firstTitle={title.notice.title}
        icon={<FaBell className={'size-6'} />}
      />

      <div className={'mt-6'}>
        {currentItems.map((notice) => (
          <Link key={notice.id} to={`${routeUrl.NOTICE.path}/${notice.id}`}>
            <div className={'mb-4 border-b border-dashed pb-2 '}>
              <div className={'mb-1 text-xs text-gray-600'}>{notice.date}</div>
              <div className={'text-base text-gray-900 hover:text-green-600'}>
                {notice.title}
              </div>
            </div>
          </Link>
        ))}
      </div>

      <PaginationComponent
        totalItems={NOTICE_LIST.length}
        itemsPerPage={ITEMS_PER_PAGE}
        currentPage={currentPage}
        onPageChange={handlePageChange}
      />
    </>
  )
}

export default Notice
