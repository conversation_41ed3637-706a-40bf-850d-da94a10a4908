import { useSelector } from 'react-redux'
import type { RootState } from '../../store/store'

const Profile = () => {
  const user = useSelector((state: RootState) => state.auth.user)

  if (!user) {
    return <p className={'text-center text-gray-500'}>No user data available</p>
  }

  return (
    <div className={'mx-auto max-w-md rounded-lg bg-white p-6 shadow-md'}>
      <h2 className={'mb-4 text-center  text-2xl font-semibold'}>Profile</h2>
      <div className={'text-lg'}>
        <p>
          <strong>Name:</strong> {user.name}
        </p>
        <p>
          <strong>Email:</strong> {user.email}
        </p>
      </div>
    </div>
  )
}

export default Profile
