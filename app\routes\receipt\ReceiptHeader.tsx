import React from 'react'
import HeaderSection from '../../components/common/Header'
import { iconSize, QuickReference } from '../../components/common/Icon'
import { routeUrl } from '../../configs/appConfig'
import title from '../../constants/title'

type ReceiptHeaderProps = {
  secondLabel: string
}

const ReceiptHeader: React.FC<ReceiptHeaderProps> = ({ secondLabel }) => {
  return (
    <HeaderSection
      breadcrumbLinks={[
        { label: title.home.title, to: '/' },
        { label: title.receiptInformation.title, to: routeUrl.RECEIPT.path },
        { label: secondLabel, isCurrent: true },
      ]}
      firstTitle={title.receiptInformation.title}
      secondTitle={secondLabel}
      icon={
        <QuickReference
          fill={''}
          width={iconSize.medium}
          height={iconSize.medium}
        />
      }
    />
  )
}

export default ReceiptHeader
