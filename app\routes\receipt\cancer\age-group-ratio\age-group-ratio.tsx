import TableauPageBase from '../../../../components/common/TableauPageBase'
import FILTER from '../../../../constants/filter'
import { TABLEAU_LINKS } from '../../../../constants/tableauLinks'
import title from '../../../../constants/title'
import { concatenateTitle } from '../../../../helper/helper'
import type { Route } from './+types/age-group-ratio'

// eslint-disable-next-line no-empty-pattern
export function meta({}: Route.MetaArgs) {
  return [
    {
      title: concatenateTitle(
        title.receiptInformation.title,
        title.receiptInformation.cancer.title,
        title.receiptInformation.cancer.child.ageGroupRatio,
      ),
    },
  ]
}

const CancerAgeGroupRatio: React.FC = () => {
  return (
    <TableauPageBase
      filterByYearProps={{
        notHasAgeGroup: true,
        hasMember: true,
      }}
      tableauWebProps={{
        src: TABLEAU_LINKS.CANCER_AGE_GROUP_RATIO,
        filterKey: [
          FILTER.modal,
          FILTER.yearParameter,
          FILTER.insurance,
          FILTER.member,
        ],
        markKey: [FILTER.fiscalYear],
        exportTitle:
          title.receiptInformation.lifestyleDisease.child.patientCount,
      }}
    />
  )
}

export default CancerAgeGroupRatio
