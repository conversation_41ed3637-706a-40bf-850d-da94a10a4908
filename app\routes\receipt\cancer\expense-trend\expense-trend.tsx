import TableauPageBase from '../../../../components/common/TableauPageBase'
import { EXPORT_TYPES } from '../../../../constants'
import { TABLEAU_LINKS } from '../../../../constants/tableauLinks'
import title from '../../../../constants/title'
import { concatenateTitle } from '../../../../helper/helper'
import type { Route } from './+types/expense-trend'

// eslint-disable-next-line no-empty-pattern
export function meta({}: Route.MetaArgs) {
  return [
    {
      title: concatenateTitle(
        title.receiptInformation.title,
        title.receiptInformation.cancer.title,
        title.receiptInformation.cancer.child.expenseTrend,
      ),
    },
    {
      tagName: 'link',
      rel: 'icon',
      href: '/favicon02.ico',
    },
  ]
}

const CancerExpendTrend: React.FC = () => {
  return (
    <TableauPageBase
      filterByYearProps={{
        notHasAgeGroup: true,
        hasCancer: true,
        hasMember: true,
        excludeExport: [EXPORT_TYPES.BID],
      }}
      tableauWebProps={{
        src: TABLEAU_LINKS.CANCER_EXPENSE_TREND,
      }}
    />
  )
}

export default CancerExpendTrend
