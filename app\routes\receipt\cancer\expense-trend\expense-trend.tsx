import TableauPageBase from '../../../../components/common/TableauPageBase'
import { EXPORT_TYPES } from '../../../../constants'
import { TABLEAU_LINKS } from '../../../../constants/tableauLinks'

const CancerExpendTrend: React.FC = () => {
  return (
    <TableauPageBase
      filterByYearProps={{
        notHasAgeGroup: true,
        hasCancer: true,
        hasMember: true,
        excludeExport: [EXPORT_TYPES.BID],
      }}
      tableauWebProps={{
        src: TABLEAU_LINKS.CANCER_EXPENSE_TREND,
      }}
    />
  )
}

export default CancerExpendTrend
