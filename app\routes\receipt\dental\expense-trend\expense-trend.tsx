import TableauPageBase from '../../../../components/common/TableauPageBase'
import { EXPORT_TYPES } from '../../../../constants'
import { TABLEAU_LINKS } from '../../../../constants/tableauLinks'
import title from '../../../../constants/title'
import { concatenateTitle } from '../../../../helper/helper'
import type { Route } from './+types/expense-trend'

// eslint-disable-next-line no-empty-pattern
export function meta({}: Route.MetaArgs) {
  return [
    {
      title: concatenateTitle(
        title.receiptInformation.title,
        title.receiptInformation.dental.title,
        title.receiptInformation.dental.child.expenseTrend,
      ),
    },
  ]
}

const DentalExpendTrend: React.FC = () => {
  return (
    <TableauPageBase
      filterByYearProps={{
        notHasAgeGroup: true,
        excludeExport: [EXPORT_TYPES.BID],
        hasMember: true,
      }}
      tableauWebProps={{
        src: TABLEAU_LINKS.DENTAL_EXPENSE_TREND,
      }}
    />
  )
}

export default DentalExpendTrend
