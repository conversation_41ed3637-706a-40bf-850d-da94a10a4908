import TableauPageBase from '../../../../components/common/TableauPageBase'
import FILTER from '../../../../constants/filter'
import { TABLEAU_LINKS } from '../../../../constants/tableauLinks'
import title from '../../../../constants/title'
import { concatenateTitle } from '../../../../helper/helper'
import type { Route } from './+types/patient-count'

// eslint-disable-next-line no-empty-pattern
export function meta({}: Route.MetaArgs) {
  return [
    {
      title: concatenateTitle(
        title.receiptInformation.title,
        title.receiptInformation.dental.title,
        title.receiptInformation.dental.child.patientCount,
      ),
    },
  ]
}

const DentalPatientCount: React.FC = () => {
  return (
    <TableauPageBase
      filterByYearProps={{
        notHasAgeGroup: true,
        hasMember: true,
      }}
      tableauWebProps={{
        src: TABLEAU_LINKS.DENTAL_PATIENT_COUNT,
        filterKey: [FILTER.modal, FILTER.yearParameter, FILTER.insurance],
        markKey: [FILTER.fiscalYear],
        exportTitle: title.receiptInformation.dental.child.patientCount,
      }}
    />
  )
}

export default DentalPatientCount
