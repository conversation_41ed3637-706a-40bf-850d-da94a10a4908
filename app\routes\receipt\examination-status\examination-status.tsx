import TableauPageBase from '../../../components/common/TableauPageBase'
import { EXAMINATION_STATUS_TAB } from '../../../constants'
import FILTER from '../../../constants/filter'
import { TABLEAU_LINKS } from '../../../constants/tableauLinks'
import title from '../../../constants/title'
import { concatenateTitle } from '../../../helper/helper'
import ReceiptHeader from '../ReceiptHeader'
import type { Route } from './+types/examination-status'

// eslint-disable-next-line no-empty-pattern
export function meta({}: Route.MetaArgs) {
  return [
    {
      title: concatenateTitle(
        title.receiptInformation.title,
        title.receiptInformation.examinationStatus.title,
        title.receiptInformation.examinationStatus.child.hospitalVisitStatus,
      ),
    },
  ]
}

const MedicalExaminationStatus: React.FC = () => {
  return (
    <TableauPageBase
      showHeader={true}
      headerSecondLabel={title.receiptInformation.examinationStatus.title}
      HeaderComponent={ReceiptHeader}
      showTabs={true}
      tabs={EXAMINATION_STATUS_TAB}
      filterByYearProps={{
        notHasAgeGroup: true,
        hasMember: true,
      }}
      tableauWebProps={{
        src: TABLEAU_LINKS.EXAMINATION_STATUS,
        filterKey: [
          FILTER.modal,
          FILTER.yearParameter,
          FILTER.insurance,
          FILTER.member,
        ],
        markKey: [FILTER.ageGroup],
        exportTitle:
          title.receiptInformation.examinationStatus.child.hospitalVisitStatus,
      }}
    />
  )
}

export default MedicalExaminationStatus
