import TableauPageBase from '../../../../components/common/TableauPageBase'
import FILTER from '../../../../constants/filter'
import { TABLEAU_LINKS } from '../../../../constants/tableauLinks'
import title from '../../../../constants/title'
import { concatenateTitle } from '../../../../helper/helper'
import type { Route } from './+types/patient-count'

// eslint-disable-next-line no-empty-pattern
export function meta({}: Route.MetaArgs) {
  return [
    {
      title: concatenateTitle(
        title.receiptInformation.title,
        title.receiptInformation.lifestyleDisease.title,
        title.receiptInformation.lifestyleDisease.child.patientCount,
      ),
    },
  ]
}

const LifeStyleDiseasePatientCount: React.FC = () => {
  return (
    <TableauPageBase
      filterByYearProps={{
        notHasAgeGroup: true,
        hasDiabetes: true,
        hasMember: true,
      }}
      tableauWebProps={{
        src: TABLEAU_LINKS.PATIENT_COUNT,
        filterKey: [
          FILTER.modal,
          FILTER.yearParameter,
          FILTER.diabetes,
          FILTER.insurance,
          FILTER.member,
        ],
        markKey: [FILTER.fiscalYear],
        exportTitle:
          title.receiptInformation.lifestyleDisease.child.patientCount,
      }}
    />
  )
}

export default LifeStyleDiseasePatientCount
