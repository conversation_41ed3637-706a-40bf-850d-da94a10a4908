import TableauPageBase from '../../../../components/common/TableauPageBase'
import FILTER from '../../../../constants/filter'
import { TABLEAU_LINKS } from '../../../../constants/tableauLinks'
import title from '../../../../constants/title'
import { concatenateTitle } from '../../../../helper/helper'
import type { Route } from './+types/annual-range'

// eslint-disable-next-line no-empty-pattern
export function meta({}: Route.MetaArgs) {
  return [
    {
      title: concatenateTitle(
        title.receiptInformation.title,
        title.receiptInformation.medicalExpense.title,
        title.receiptInformation.medicalExpense.child.annualRange,
      ),
    },
  ]
}

const AnnualRange: React.FC = () => {
  return (
    <TableauPageBase
      filterByYearProps={{
        notHasAgeGroup: true,
        hasMember: true,
      }}
      tableauWebProps={{
        src: TABLEAU_LINKS.ANNUAL,
        filterKey: [
          FILTER.modal,
          FILTER.yearParameter,
          FILTER.insurance,
          FILTER.member,
        ],
        markKey: [FILTER.fiscalYear],
        exportTitle: title.receiptInformation.medicalExpense.child.annualRange,
      }}
    />
  )
}

export default AnnualRange
