import TableauPageBase from '../../../../components/common/TableauPageBase'
import { EXPORT_TYPES } from '../../../../constants'
import { TABLEAU_LINKS } from '../../../../constants/tableauLinks'
import title from '../../../../constants/title'
import { concatenateTitle } from '../../../../helper/helper'
import type { Route } from './+types/cost-analysis'

// eslint-disable-next-line no-empty-pattern
export function meta({}: Route.MetaArgs) {
  return [
    {
      title: concatenateTitle(
        title.receiptInformation.title,
        title.receiptInformation.medicalExpense.title,
        title.receiptInformation.medicalExpense.child.costAnalysis,
      ),
    },
  ]
}

const CostAnalysis: React.FC = () => {
  return (
    <>
      <p className={'mt-6 text-sm font-normal text-gray-900'}>
        ※合計医療費が年度あたり100万円以上の患者を対象としています。
      </p>
      <TableauPageBase
        filterByYearProps={{
          notHasAgeGroup: true,
          hasMember: true,
          excludeExport: [EXPORT_TYPES.BID],
        }}
        tableauWebProps={{
          src: TABLEAU_LINKS.COST,
        }}
      />
    </>
  )
}

export default CostAnalysis
