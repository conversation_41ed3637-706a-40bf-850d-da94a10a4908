import TableauPageBase from '../../../../components/common/TableauPageBase'
import { EXPORT_TYPES } from '../../../../constants'
import { TABLEAU_LINKS } from '../../../../constants/tableauLinks'
import title from '../../../../constants/title'
import { concatenateTitle } from '../../../../helper/helper'
import type { Route } from './+types/count'

// eslint-disable-next-line no-empty-pattern
export function meta({}: Route.MetaArgs) {
  return [
    {
      title: concatenateTitle(
        title.receiptInformation.title,
        title.receiptInformation.medicalExpense.title,
        title.receiptInformation.medicalExpense.child.receiptCount,
      ),
    },
  ]
}

const Count: React.FC = () => {
  return (
    <TableauPageBase
      filterByYearProps={{
        hasFilterRadio: true,
        hasMember: true,
        excludeExport: [EXPORT_TYPES.BID],
      }}
      tableauWebProps={{
        src: TABLEAU_LINKS.COUNT,
      }}
    />
  )
}

export default Count
