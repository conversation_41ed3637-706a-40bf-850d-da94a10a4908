import { useEffect, useState } from 'react'
import { Link } from 'react-router-dom'
import HeaderSection from '../../components/common/Header'
import {
  ExpandCircleRight,
  iconSize,
  QuickReference,
} from '../../components/common/Icon'

import SegmentedControl from '../../components/common/ToggleButton'
import TrendDisplay from '../../components/common/Trendisplay'
import WithLoading from '../../components/common/WithLoading'
import TableauWeb from '../../components/tableau/tableauWeb'
import TableauWebEnhanced from '../../components/tableau/TableauWebEnhanced'
import TableauWebDataTest from '../../components/test/TableauWebDataTest'
import { routeUrl } from '../../configs/appConfig'
import { PERIOD } from '../../constants/index'
import {
  TABLEAU_LINKS,
  TABLEAU_STORAGE_KEYS,
} from '../../constants/tableauLinks'
import title from '../../constants/title'
import { formatNumber, formatYearMonth } from '../../helper/helper'

const Receipt: React.FC = () => {
  const [dataSheet, setDataSheet] = useState<any[]>([])
  const [tableauSrc, setTableauSrc] = useState(TABLEAU_LINKS.RECEIPT)

  const handleData = (data: any[]) => {
    setDataSheet(data)
  }

  useEffect(() => {
    const fetchDataFromLocalStorage = () => {
      const cachedData = localStorage.getItem(TABLEAU_STORAGE_KEYS.RECEIPT_DATA)
      if (cachedData) {
        setDataSheet(JSON.parse(cachedData))
      }
    }
    fetchDataFromLocalStorage()
  }, [])

  const handleSegmentChange = (value: string) => {
    if (value === PERIOD.MONTH) {
      setTableauSrc(TABLEAU_LINKS.RECEIPT)
    } else if (value === PERIOD.YEAR) {
      setTableauSrc(TABLEAU_LINKS.RECEIPT_YEAR)
    }
  }

  return (
    <div>
      <HeaderSection
        breadcrumbLinks={[
          { label: title.home.title, to: '/' },
          { label: title.receiptInformation.title, isCurrent: true },
        ]}
        firstTitle={title.receiptInformation.title}
        icon={
          <QuickReference
            fill={''}
            width={iconSize.medium}
            height={iconSize.medium}
          />
        }
      />

      <p className={'mt-2 text-sm text-gray-900'}>
        {title.receiptInformation.description1}
      </p>
      <p className={'text-sm-bold text-gray-900'}>
        {title.receiptInformation.description2}
      </p>

      <div className={'mt-4 rounded-lg bg-green-50 p-8 shadow-md'}>
        <div className={'grid h-[470px] grid-cols-12 gap-8'}>
          <div className={'col-span-7 space-y-4 '}>
            <div className={'rounded-2xl border border-gray-100 bg-white p-4'}>
              <div className={'col-span-12 flex items-center justify-between'}>
                <Link
                  to={routeUrl.RECEIPT.children.MEDICAL_EXPENSE.path}
                  className={
                    'group flex flex-1 items-center gap-2 hover:text-green-600'
                  }
                >
                  <div className={'flex items-center gap-2'}>
                    <span
                      className={
                        'text-sm-bold text-gray-900 group-hover:text-green-600'
                      }
                    >
                      {title.receiptInformation.receiptTotalAmount}
                    </span>
                    <ExpandCircleRight />
                  </div>
                </Link>
                <button className={'text-blue-500'}>
                  <SegmentedControl
                    options={[
                      { label: '月', value: PERIOD.MONTH },
                      { label: '年度', value: PERIOD.YEAR },
                    ]}
                    onChange={handleSegmentChange}
                  />
                </button>
              </div>
              <div
                className={'mt-4 flex h-[256px] justify-center overflow-hidden'}
              >
                <TableauWeb isSummary={true} src={tableauSrc} />
              </div>
              {/* Thử dùng TableauWebEnhanced (dựa trên TableauViz) */}
              <TableauWebEnhanced
                src={TABLEAU_LINKS.NUMBER_SUMMARY}
                dataSheet={handleData}
                isData={true}
                storageKey={TABLEAU_STORAGE_KEYS.RECEIPT_DATA}
              />
            </div>
            <Link
              to={routeUrl.RECEIPT.children.MEDICAL_EXPENSE.path}
              className={'group block'}
            >
              <div
                className={
                  'h-[132px] cursor-pointer rounded-2xl border border-gray-100 bg-white p-4 shadow-md hover:border-2 hover:border-green-600 hover:text-green-600'
                }
              >
                <WithLoading isLoading={dataSheet?.length === 0}>
                  <div className={'flex items-center gap-2'}>
                    <span
                      className={
                        'text-sm-bold text-gray-900 group-hover:text-green-600'
                      }
                    >
                      {title.receiptInformation.medicalTotalAmount}
                    </span>
                    <ExpandCircleRight />
                  </div>
                  <div className={'text-right'}>
                    <TrendDisplay
                      currentValue={formatNumber(dataSheet?.[11]?.[1]?._value)}
                      trendValue={formatNumber(dataSheet?.[10]?.[1]?._value)}
                      previousValue={formatNumber(dataSheet?.[9]?.[1]?._value)}
                      unit={'(百万円)'}
                    />
                  </div>
                </WithLoading>
              </div>
            </Link>
          </div>

          <div className={'col-span-5'}>
            <Link
              to={routeUrl.RECEIPT.children.EXAMINATION_STATUS.path}
              className={'group block'}
            >
              <div
                className={
                  'group h-[138px] cursor-pointer rounded-2xl border border-gray-100 bg-white p-4 shadow-md hover:border-2 hover:border-green-600 hover:text-green-600'
                }
              >
                <WithLoading isLoading={dataSheet?.length === 0}>
                  <div className={'flex items-center gap-2'}>
                    <span
                      className={
                        'cursor-pointer text-sm-bold text-gray-900 group-hover:text-green-600'
                      }
                    >
                      {title.receiptInformation.medicalInstitutionVisitRate}
                    </span>
                    <ExpandCircleRight />
                  </div>
                  <div className={'text-right'}>
                    <TrendDisplay
                      currentValue={formatNumber(
                        dataSheet?.[8]?.[1]?._value,
                        true,
                      )}
                      trendValue={formatNumber(
                        dataSheet?.[7]?.[1]?._value,
                        true,
                      )}
                      previousValue={formatNumber(
                        dataSheet?.[6]?.[1]?._value,
                        true,
                      )}
                      unit={'%'}
                    />
                  </div>
                </WithLoading>
              </div>
            </Link>
            <Link
              to={routeUrl.RECEIPT.children.EXAMINATION_STATUS.path}
              className={'group block'}
            >
              <div
                className={
                  'group mt-4 h-[138px] cursor-pointer rounded-2xl border border-gray-100 bg-white p-4 shadow-md hover:border-2 hover:border-green-600 hover:text-green-600'
                }
              >
                <WithLoading isLoading={dataSheet?.length === 0}>
                  <div className={'flex items-center gap-2'}>
                    <span
                      className={
                        'cursor-pointer text-sm-bold text-gray-900 group-hover:text-green-600'
                      }
                    >
                      {title.receiptInformation.actualPatientNumber}
                    </span>
                    <ExpandCircleRight />
                  </div>
                  <div className={'text-right'}>
                    <TrendDisplay
                      currentValue={formatNumber(dataSheet?.[5]?.[1]?._value)}
                      trendValue={formatNumber(dataSheet?.[4]?.[1]?._value)}
                      previousValue={formatNumber(dataSheet?.[3]?.[1]?._value)}
                      unit={'人'}
                    />
                  </div>
                </WithLoading>
              </div>
            </Link>
            <Link
              to={routeUrl.RECEIPT.children.MEDICAL_EXPENSE.path}
              className={'group block'}
            >
              <div
                className={
                  'group mt-4 h-[138px] cursor-pointer rounded-2xl border border-gray-100 bg-white p-4 shadow-md hover:border-2 hover:border-green-600 hover:text-green-600'
                }
              >
                <WithLoading isLoading={dataSheet?.length === 0}>
                  <div className={'flex items-center gap-2'}>
                    <span
                      className={
                        'cursor-pointer text-sm-bold text-gray-900 group-hover:text-green-600'
                      }
                    >
                      {title.receiptInformation.perPersonMedicalExpense}
                    </span>
                    <ExpandCircleRight />
                  </div>
                  <div className={'text-right'}>
                    <TrendDisplay
                      currentValue={formatNumber(dataSheet?.[2]?.[1]?._value)}
                      trendValue={formatNumber(dataSheet?.[1]?.[1]?._value)}
                      previousValue={formatNumber(dataSheet?.[0]?.[1]?._value)}
                      unit={'円'}
                    />
                  </div>
                </WithLoading>
              </div>
            </Link>
            <div className={'mt-2 text-right text-xs text-gray-900'}>
              ※集計期間：前年度 {formatYearMonth(dataSheet?.[12]?.[1]?._value)}-
              {formatYearMonth(dataSheet?.[13]?.[1]?._value)}、今年度{' '}
              {formatYearMonth(dataSheet?.[14]?.[1]?._value)}-
              {formatYearMonth(dataSheet?.[15]?.[1]?._value)}
            </div>
          </div>
        </div>
      </div>

      <div className={'mt-4 rounded-lg bg-green-50 p-8 shadow-md'}>
        <div className={'flex items-center justify-between'}>
          <p className={'text-base-bold text-gray-600'}>
            {title.diseaseAnalysis}
          </p>
        </div>
        <div
          className={
            'mt-2 flex items-center gap-4 text-center text-base-bold text-gray-900'
          }
        >
          <Link
            to={routeUrl.RECEIPT.children.LIFESTYLE_DISEASE.path}
            className={'group block'}
          >
            <div
              className={
                'flex h-[60px] w-[247px] cursor-pointer items-center justify-center rounded-2xl bg-white shadow-md hover:border-2 hover:border-green-600 hover:text-green-600'
              }
            >
              {title.receiptInformation.lifestyleDisease.title}
            </div>
          </Link>
          <Link
            to={routeUrl.RECEIPT.children.CANCER.path}
            className={'group block'}
          >
            <div
              className={
                'flex h-[60px] w-[247px] cursor-pointer items-center justify-center rounded-2xl bg-white shadow-md hover:border-2 hover:border-green-600 hover:text-green-600'
              }
            >
              {title.receiptInformation.cancer.title}
            </div>
          </Link>
          <Link
            to={routeUrl.RECEIPT.children.DENTAL.path}
            className={'group block'}
          >
            <div
              className={
                'flex h-[60px] w-[247px] cursor-pointer items-center justify-center rounded-2xl bg-white shadow-md hover:border-2 hover:border-green-600 hover:text-green-600'
              }
            >
              {title.receiptInformation.dental.title}
            </div>
          </Link>
        </div>
      </div>

      {/* Test component để kiểm tra tính năng TableauWeb data only */}
      <TableauWebDataTest />
    </div>
  )
}

export default Receipt
