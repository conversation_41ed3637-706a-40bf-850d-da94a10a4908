import type { User } from '../types/user'

declare global {
  interface Window {
    dataLayer: any[]
  }
}

class AnalyticsService {
  private static currentUid: string | number | null = null
  private static currentEmplrId: string | number | null = null

  public static setUserParameters(user: User | null): void {
    if (typeof window === 'undefined') return

    window.dataLayer = window.dataLayer || []

    const newUid = user?.id || null
    const newEmplrId = user?.emplrId || null

    if (this.currentUid !== newUid || this.currentEmplrId !== newEmplrId) {
      this.currentUid = newUid
      this.currentEmplrId = newEmplrId

      window.dataLayer.push({
        uid: newUid,
        emplr_id: newEmplrId,
      })
    }
  }
}

export default AnalyticsService
