import api from '../core/service/api'

import type { AuthResponse, LoginRequest, RegisterRequest } from '../types/auth'

export const login = async (data: LoginRequest): Promise<AuthResponse> => {
  const response = await api.post('/login', data)
  return response.data.data
}

export const register = async (
  data: RegisterRequest,
): Promise<AuthResponse> => {
  const response = await api.post('/register', data)
  return response.data.data
}

export const getProfile = async (): Promise<AuthResponse> => {
  const response = await api.get('/profile')
  return response.data.data
}

export const getMaintainMode = async (): Promise<boolean> => {
  const response = await api.get('/maintain-mode')
  return response.data.data.is_enabled
}
