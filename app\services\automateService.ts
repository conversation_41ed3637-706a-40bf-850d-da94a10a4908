import api from '../core/service/api'

export const downloadPdf = async (payload: any) => {
  try {
    const response = await api.post(
      '/api/tableau/views/download-pdf',
      payload,
      {
        responseType: 'blob',
      },
    )
    return response.data
  } catch (error) {
    console.error('Failed to download PDF:', error)
    throw error
  }
}

export const callAutomateApi = async (token: string, url: string) => {
  try {
    const response = await api.post('/api/automate', { token, url })
    return response.data
  } catch (error) {
    console.error('Failed to call automate API:', error)
    throw error
  }
}
