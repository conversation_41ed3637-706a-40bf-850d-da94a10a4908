import api from '../core/service/api'

export const downloadPdf = async (payload: {
  viewId: string
  filters?: Record<string, any>
}): Promise<Blob> => {
  try {
    const requestBody = {
      viewId: payload.viewId,
      filters: payload.filters || {},
    }

    const pdfResponse = await api.post(
      '/api/tableau/download/pdf',
      requestBody,
      { responseType: 'blob' },
    )

    return pdfResponse.data
  } catch (error) {
    console.error('Error in downloadPdf:', error)
    throw error
  }
}

export const downloadCsv = async (payload: {
  viewId: string
  filters?: Record<string, any>
}): Promise<Blob> => {
  try {
    const requestBody = {
      viewId: payload.viewId,
      filters: payload.filters || {},
    }

    const csvResponse = await api.post(
      '/api/tableau/download/csv',
      requestBody,
      { responseType: 'blob' },
    )

    return csvResponse.data
  } catch (error) {
    console.error('Error in downloadCsv:', error)
    throw error
  }
}
