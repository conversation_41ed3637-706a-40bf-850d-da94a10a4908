import api from '../core/service/api'

export const getUser = async () => {
  const response = await api.get('/api/user-info')
  return response.data
}

export const getUsersByUnions = async (keyword: string) => {
  const response = await api.get(`/api/users-by-union?keyword=${keyword}`)
  return response.data
}

export const getUnions = async (keyword: string) => {
  const response = await api.get(`/api/emplrs?keyword=${keyword}`)
  return response.data
}
