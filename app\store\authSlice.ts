import type { PayloadAction } from '@reduxjs/toolkit'
import { createSlice } from '@reduxjs/toolkit'
import BaseAuthService from '../core/service/baseAuthService'
import AnalyticsService from '../services/analyticsService'
import type { User } from '../types/user'
type AuthState = {
  user: User | null
  token: string | null
}

const initialState: AuthState = {
  user: null,
  token: null,
}

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    setUser: (state, action: PayloadAction<User>) => {
      state.user = action.payload
      AnalyticsService.setUserParameters(action.payload)
    },
    setToken: (state, action: PayloadAction<string>) => {
      state.token = action.payload
      BaseAuthService.setAccessToken(action.payload)
    },
    logout: (state) => {
      AnalyticsService.setUserParameters(null)
      state.user = null
      state.token = null
      BaseAuthService.removeAccessToken()
    },
  },
})

export const { setUser, setToken, logout } = authSlice.actions
export default authSlice.reducer
