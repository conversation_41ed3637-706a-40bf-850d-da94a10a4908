import js from '@eslint/js'
import importPlugin from 'eslint-plugin-import'
import react from 'eslint-plugin-react'
import reactCompiler from 'eslint-plugin-react-compiler'
import reactHooks from 'eslint-plugin-react-hooks'
import reactRefresh from 'eslint-plugin-react-refresh'
import tailwindcss from 'eslint-plugin-tailwindcss'
import { defineConfig, globalIgnores } from 'eslint/config'
import globals from 'globals'
import tseslint from 'typescript-eslint'

export default defineConfig([
  globalIgnores([
    'dist',
    'build',
    'public',
    'storybook-static',
    '**/mockServiceWorker.js',
    '**/types.generated.ts',
    '.react-router',
    'playwright-report',
  ]),

  // JavaScript
  {
    files: ['**/*.{js,ts,tsx}'],
    rules: {
      ...js.configs.recommended.rules,
    },
  },

  // TypeScript
  ...tseslint.config({
    extends: [...tseslint.configs.recommended],
    plugins: {
      import: importPlugin,
    },
    files: ['**/*.{ts,tsx}'],
    languageOptions: {
      ecmaVersion: 2020,
      globals: globals.browser,
      parserOptions: {
        projectService: true,
        tsconfigRootDir: import.meta.dirname,
      },
    },
    rules: {
      '@typescript-eslint/consistent-type-definitions': ['error', 'type'],
      '@typescript-eslint/consistent-type-imports': [
        'error',
        { prefer: 'type-imports' },
      ],

      '@typescript-eslint/naming-convention': [
        'error',
        {
          selector: ['interface', 'typeAlias'],
          format: ['PascalCase'],
        },
      ],

      '@typescript-eslint/no-unnecessary-condition': 'error',

      'import/consistent-type-specifier-style': ['error', 'prefer-top-level'],

      '@typescript-eslint/no-explicit-any': 'off', // 既存コードに違反が多いためひとまずオフにします。
    },
  }),

  // React
  {
    files: ['**/*.{ts,tsx}'],
    plugins: {
      react,
      'react-compiler': reactCompiler,
      'react-hooks': reactHooks,
      'react-refresh': reactRefresh,
    },
    settings: {
      react: {
        version: 'detect',
      },
    },
    rules: {
      ...reactHooks.configs.recommended.rules,
      'react-refresh/only-export-components': [
        'error',
        {
          allowConstantExport: true,
          allowExportNames: [
            'meta',
            'links',
            'clientLoader',
            'clientAction',
            'HydrateFallback',
            'ErrorBoundary',
          ],
        },
      ],
      'react/no-unescaped-entities': 'error',
      'react/self-closing-comp': [
        'error',
        {
          component: true,
          html: true,
        },
      ],

      'react/jsx-key': 'error',
      'react/jsx-boolean-value': ['error', 'always'],
      'react/jsx-curly-brace-presence': ['error', { props: 'always' }],
      'react/jsx-no-target-blank': 'error',

      'react-compiler/react-compiler': 'error',
    },
  },

  // Tailwind CSS
  {
    files: ['**/*.{ts,tsx}'],
    plugins: {
      tailwindcss,
    },
    rules: {
      'tailwindcss/classnames-order': 'error',
      'tailwindcss/enforces-negative-arbitrary-values': 'error',
      'tailwindcss/enforces-shorthand': 'error',
      'tailwindcss/migration-from-tailwind-2': 'error',
      'tailwindcss/no-arbitrary-value': 'off',
      'tailwindcss/no-contradicting-classname': 'error',
      'tailwindcss/no-custom-classname': 'error',
      'tailwindcss/no-unnecessary-arbitrary-value': 'error',
    },
  },
])
