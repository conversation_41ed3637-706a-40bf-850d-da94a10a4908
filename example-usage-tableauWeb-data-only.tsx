// V<PERSON> dụ sử dụng TableauWeb với tính năng chỉ lấy dữ liệu (không hiển thị biểu đồ)

import React, { useState, useEffect } from 'react'
import TableauWeb from '../components/tableau/tableauWeb'
import { TABLEAU_LINKS } from '../constants/tableauLinks'
import { TABLEAU_STORAGE_KEYS } from '../constants/tableauLinks'

const ExampleDataOnlyUsage: React.FC = () => {
  const [dataSheet, setDataSheet] = useState<any[]>([])

  // Hàm xử lý dữ liệu khi nhận được từ Tableau
  const handleData = (data: any[]) => {
    setDataSheet(data)
    console.log('Dữ liệu nhận được từ Tableau:', data)
  }

  // Load dữ liệu từ localStorage khi component mount
  useEffect(() => {
    const fetchDataFromLocalStorage = () => {
      const cachedData = localStorage.getItem(TABLEAU_STORAGE_KEYS.EXAMPLE_DATA)
      if (cachedData) {
        setDataSheet(JSON.parse(cachedData))
      }
    }
    fetchDataFromLocalStorage()
  }, [])

  return (
    <div>
      <h1>Ví dụ sử dụng TableauWeb chỉ để lấy dữ liệu</h1>
      
      {/* Hiển thị dữ liệu đã lấy được */}
      {dataSheet.length > 0 && (
        <div>
          <h2>Dữ liệu đã lấy được:</h2>
          <pre>{JSON.stringify(dataSheet, null, 2)}</pre>
        </div>
      )}

      {/* TableauWeb component ẩn, chỉ để lấy dữ liệu */}
      <div style={{ opacity: 0, width: 0, height: 0, overflow: 'hidden' }}>
        <TableauWeb
          src={TABLEAU_LINKS.EXAMPLE_DATA_SOURCE}
          dataSheet={handleData}           // Callback để nhận dữ liệu
          isData={true}                    // Bật chế độ chỉ lấy dữ liệu
          storageKey={TABLEAU_STORAGE_KEYS.EXAMPLE_DATA} // Key để lưu vào localStorage
        />
      </div>
    </div>
  )
}

export default ExampleDataOnlyUsage
