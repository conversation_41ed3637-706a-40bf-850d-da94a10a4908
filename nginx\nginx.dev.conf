worker_processes auto;

events {
    worker_connections 8000;
    multi_accept on;
}


http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    log_format compression '$remote_addr - $remote_user [$time_local] '
        '"$request" $status $upstream_addr '
        '"$http_referer" "$http_user_agent"';

    server {
        listen 80;
        access_log /var/log/nginx/access.log compression;

        root /usr/share/nginx/html;
        index index.html index.htm;

        location /healthcheck {
            auth_basic off;
            return 200 'OK';
            add_header Content-Type text/plain;
        }


        location / {
            # auth_basic "Restricted Access";
            # auth_basic_user_file /etc/nginx/.htpasswd;

            try_files $uri $uri/ /index.html;
        }

        location ~* \.(?:jpg|jpeg|gif|png|ico|cur|gz|svg|svgz|mp4|ogg|ogv|webm|htc)$ {
          expires 1M;
          access_log off;
          add_header Cache-Control "public";
        }

        location ~* \.(?:css|js)$ {
            try_files $uri =404;
            expires 1y;
            access_log off;
            add_header Cache-Control "public";
        }

        location ~ ^.+\..+$ {
            try_files $uri =404;
        }
    }
}
