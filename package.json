{"name": "rakuraku-fe", "private": true, "version": "0.0.0", "type": "module", "scripts": {"build": "react-router build", "dev": "react-router dev", "start": "react-router-serve ./build/server/index.js", "lint": "eslint .", "preview": "vite preview", "format": "prettier --write .", "typecheck": "react-router typegen && tsc"}, "dependencies": {"@auth0/auth0-react": "2.4.0", "@emotion/react": "11.14.0", "@emotion/styled": "11.14.1", "@mui/icons-material": "7.2.0", "@mui/material": "7.2.0", "@mui/system": "7.2.0", "@mui/utils": "7.2.0", "@react-router/node": "7.7.1", "@react-router/serve": "7.7.1", "@reduxjs/toolkit": "2.8.2", "@types/react-window": "1.8.8", "axios": "1.11.0", "clsx": "2.1.1", "isbot": "5.1.29", "js-cookie": "3.0.5", "react": "19.1.1", "react-dom": "19.1.1", "react-icons": "5.5.0", "react-redux": "9.2.0", "react-router": "7.7.1", "react-router-dom": "7.7.1", "react-window": "1.8.11", "yup": "1.7.0"}, "devDependencies": {"@eslint/js": "9.32.0", "@react-router/dev": "7.7.1", "@testing-library/dom": "10.4.1", "@testing-library/jest-dom": "6.6.4", "@testing-library/react": "16.3.0", "@types/js-cookie": "3.0.6", "@types/node": "22.17.0", "@types/react": "19.1.9", "@types/react-dom": "19.1.7", "@vitejs/plugin-react": "4.7.0", "@yarnpkg/types": "4.0.1", "autoprefixer": "10.4.21", "eslint": "9.32.0", "eslint-plugin-import": "2.32.0", "eslint-plugin-react": "7.37.5", "eslint-plugin-react-compiler": "19.1.0-rc.2", "eslint-plugin-react-hooks": "5.2.0", "eslint-plugin-react-refresh": "0.4.20", "eslint-plugin-tailwindcss": "3.18.2", "globals": "16.3.0", "jsdom": "26.1.0", "postcss": "8.5.6", "prettier": "3.6.2", "prettier-plugin-organize-imports": "4.2.0", "tailwindcss": "3.4.17", "typescript": "5.9.2", "typescript-eslint": "8.39.0", "vite": "7.0.6", "vitest": "3.2.4"}, "packageManager": "yarn@4.9.2"}