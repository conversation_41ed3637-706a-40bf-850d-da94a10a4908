::-webkit-scrollbar {
  width: 12px;
}

::-webkit-scrollbar-track {
  background: #E6E6E6;
  border-radius: 8px;
}

::-webkit-scrollbar-thumb {
  background: #999999;
  border-radius: 8px;
}

body {
  background-color: #faf9f9;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  font-family: 'Helvetica Neue', 'Arial', 'Hiragino Kaku Gothic ProN', 'Hiragino Sans', 'Meiryo', sans-serif !important;
}

.header {
  background-color: #ffffff;
  color: #000000;
  padding: 12px;
  border-bottom: 3px solid #068667;
  height: 56px;
}

.home-button {
  width: 400px;
  height: 120px;
  background-color: #e6e6e6;
  margin: 83px auto 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.notice-container {
  width: 880px;
  margin: 64px auto 0;
}

.notice-box {
  margin-top: 8px;
  padding: 16px;
  background-color: #ffffff;
  border-radius: 8px;
  border: 1px solid #b3b3b3;
}

.notice-list {
  overflow-y: auto;
  width: 100%;
  height: 240px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding-right: 16px;
}

.maintenance-message {
  margin-top: 64px;
  display: flex;
  flex-direction: column;
  align-items: center;
} 
