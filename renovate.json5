{
  $schema: 'https://docs.renovatebot.com/renovate-schema.json',
  extends: ['config:recommended'],
  major: {
    dependencyDashboardApproval: true, // major update PR は手動で作成する
  },
  packageRules: [
    {
      matchManagers: ['npm'],
      postUpdateOptions: ['yarnDedupeHighest'],
    },
    {
      // package.json の packageManager フィールドの更新は自動マージします。
      matchDepTypes: ['packageManager'],
      automerge: true,
    },
    {
      // .nvmrc の更新は自動マージします。
      matchManagers: ['nvm'],
      automerge: true,
    },
    {
      // npm パッケージは 公開後 3 日以内であれば取り下げることができます。PR 作成まで 3 日待つことでバージョン取り下げによる影響を回避できます。
      // https://docs.renovatebot.com/configuration-options/#prevent-holding-broken-npm-packages
      matchManagers: ['npm'],
      minimumReleaseAge: '3 days',
    },
    {
      // devDependencies の patch, minor バージョンアップは自動マージします。
      matchDepTypes: ['devDependencies'],
      matchUpdateTypes: ['patch', 'minor'],
      automerge: true,
    },
  ],
}
