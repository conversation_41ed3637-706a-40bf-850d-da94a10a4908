#!/bin/sh
# filepath: scripts/pre-commit-sonar.sh

# This script is a pre-commit hook for running SonarScanner on the CQC project.
ENV_FILE="$(git rev-parse --show-toplevel)/.env"
if [ -f "$ENV_FILE" ]; then
  . "$ENV_FILE"
else
  echo "Error: .env file not found at $ENV_FILE"
  exit 1
fi
set +o allexport

if [ "$ENABLE_SONAR_CHECK" != "1" ]; then
  echo "Sonar check is disabled. Skipping SonarScanner."
  exit 0
fi

echo "Running SonarScanner for CQC..."
sonar-scanner \
  -Dsonar.projectKey=$SONAR_PROJECT_KEY \
  -Dsonar.host.url=$SONAR_HOST_URL \
  -Dsonar.login=$SONAR_TOKEN

if [ $? -ne 0 ]; then
  echo "SonarScanner failed. Please fix the issues before committing."
  exit 1
fi
