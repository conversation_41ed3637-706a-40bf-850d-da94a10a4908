/** @type {import('tailwindcss').Config} */
export default {
  content: ['./index.html', './app/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      screens: {
        'h-sm': { raw: '(max-height: 800px)' },
      },
      fontSize: {
        xxs: ['10px', { fontWeight: '400', lineHeight: '12px' }],
        xs: ['12px', { fontWeight: '400', lineHeight: '16px' }],
        'xs-bold': ['12px', { fontWeight: '600', lineHeight: '16px' }],
        sm: ['14px', { fontWeight: '400', lineHeight: '20px' }],
        'sm-bold': ['14px', { fontWeight: '600', lineHeight: '20px' }],
        base: ['16px', { fontWeight: '400', lineHeight: '24px' }],
        'base-bold': ['16px', { fontWeight: '600', lineHeight: '24px' }],
        lg: ['18px', { fontWeight: '400', lineHeight: '28px' }],
        'lg-bold': ['18px', { fontWeight: '600', lineHeight: '28px' }],
        xl: ['20px', { fontWeight: '400', lineHeight: '28px' }],
        'xl-bold': ['20px', { fontWeight: '600', lineHeight: '28px' }],
        '2xl': ['24px', { fontWeight: '400', lineHeight: '32px' }],
        '2xl-bold': ['24px', { fontWeight: '600', lineHeight: '32px' }],
        '3xl': ['30px', { fontWeight: '400', lineHeight: '36px' }],
        '4xl': ['36px', { fontWeight: '400', lineHeight: '40px' }],
        '5xl': ['48px', { fontWeight: '400', lineHeight: '48px' }],
        '6xl': ['60px', { fontWeight: '400', lineHeight: '60px' }],
      },
      fontFamily: {
        sans: [
          'Helvetica Neue',
          'Arial',
          'Hiragino Kaku Gothic ProN',
          'Hiragino Sans',
          'Meiryo',
          'sans-serif',
        ],
      },
      colors: {
        gray: {
          50: '#F2F2F2',
          100: '#E6E6E6',
          200: '#CCCCCC',
          300: '#B3B3B3',
          400: '#999999',
          500: '#7F7F7F',
          600: '#666666',
          700: '#4D4D4D',
          800: '#333333 ',
          900: '#1A1A1A',
        },
        blueGray: {
          50: '#E5E9EC',
          100: '#CFD8DC',
          200: '#B0BEC5',
          300: '#90A4AE',
          400: '#78909C',
          500: '#607D8B',
          600: '#546E7A',
          700: '#455A64',
          800: '#37474F ',
          900: '#263238',
        },
        cyan: {
          50: '#E9F7F9',
          100: '#C8F8FF',
          200: '#99F2FF',
          300: '#79E2F2',
          400: '#2BC8E4',
          500: '#01B7D6',
          600: '#00A3BF',
          700: '#008299',
          800: '#006F83 ',
          900: '#004C59',
        },
        blue: {
          50: '#E8F1FE',
          100: '#D9E6FF',
          200: '#C5D7FB',
          300: '#9DB7F9',
          400: '#7096F8',
          500: '#4979F5',
          600: '#3460FB',
          700: '#264AF4',
          800: '#0017C1 ',
          900: '#000071',
        },
        blueGreen: {
          50: '#F2F9F6',
          100: '#C1E9E5',
          200: '#A2DDD9',
          300: '#83D2CC',
          400: '#64C7BF',
          500: '#4E9D95',
          600: '#22847B',
          700: '#1B6A62',
          800: '#144F4A ',
          900: '#0E3531',
        },
        green: {
          50: '#F4F8F6',
          100: '#D3F1EA',
          200: '#B4DBD1',
          300: '#9BCFC2',
          400: '#6AB6A4',
          500: '#389E85',
          600: '#068667',
          700: '#056B52',
          800: '#04503E ',
          900: '#023629',
        },
        lime: {
          50: '#EBFAD9',
          100: '#D0F5A2',
          200: '#ADE830',
          300: '#9DDD15',
          400: '#8CC80C',
          500: '#7EB40D',
          600: '#6FA104',
          700: '#507500',
          800: '#3E5A00 ',
          900: '#2C4100',
        },
        yellow: {
          50: '#FBF5E0',
          100: '#FFF0B3',
          200: '#FFE380',
          300: '#FFD43D',
          400: '#FFC700',
          500: '#EBB700',
          600: '#B78F00',
          700: '#927200',
          800: '#806300 ',
          900: '#604B00',
        },
        orange: {
          50: '#FFEEE2',
          100: '#FFDFCA',
          200: '#FFC199',
          300: '#FFA66D',
          400: '#FF8D44',
          500: '#FF7628',
          600: '#E25100',
          700: '#C74700',
          800: '#8B3200 ',
          900: '#541E00',
        },
        red: {
          50: '#FDEEEE',
          100: '#FFDADA',
          200: '#FFBBBB',
          300: '#FF9696',
          400: '#FF7171',
          500: '#FF5454',
          600: '#FE3939',
          700: '#EC0000',
          800: '#A90000 ',
          900: '#850000',
        },
        beige: {
          400: '#FAF9F9',
        },
      },
      boxShadow: {
        sm: '0px 1px 2px 0px rgba(0, 0, 0, 0.05)',
        'base-1': '0px 1px 2px 0px rgba(0, 0, 0, 0.06)',
        'base-2': '1px 2px 3px 0px rgba(0, 0, 0, 0.10)',
        'md-1': '0px 2px 4px -1px rgba(0, 0, 0, 0.06)',
        'md-2': '0px 4px 6px -1px rgba(0, 0, 0, 0.10)',
        'lg-1': '0px 4px 8px -2px rgba(0, 0, 0, 0.10)',
        'lg-2': '0px 8px 16px -2px rgba(0, 0, 0, 0.10)',
        'xl-1': '0px 10px 10px -5px rgba(0, 0, 0, 0.04)',
        'xl-2': '0px 20px 25px -5px rgba(0, 0, 0, 0.10)',
        '2xl': '0px 25px 50px -12px rgba(0, 0, 0, 0.25)',
        inner: 'inset 0px 2px 4px 0px rgba(0, 0, 0, 0.06)',
      },
      borderRadius: {
        none: '0px',
        sm: '2px',
        default: '4px',
        lg: '6px',
        xl: '8px',
        '2xl': '12px',
        '3xl': '16px',
        '4xl': '24px',
        full: '999px',
      },
    },
  },
  plugins: [],
}
