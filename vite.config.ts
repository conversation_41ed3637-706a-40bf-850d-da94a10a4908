import { reactRouter } from '@react-router/dev/vite'
import { defineConfig } from 'vite'
const { NODE_ENV } = process.env

// https://vite.dev/config/
export default defineConfig({
  plugins: [reactRouter()],
  server: {
    host: '0.0.0.0',
    port: 3000,
  },
  ssr: {
    noExternal:
      NODE_ENV === 'production' ?
        [
          '@mui/system',
          '@mui/material',
          '@mui/x-date-pickers',
          '@mui/utils',
          '@mui/x-data-grid',
          '@mui/styled-engine',
        ]
      : [],
  },
  optimizeDeps: {
    include: ['@mui/*', '@emotion/*'],
    force: true,
  },
})
