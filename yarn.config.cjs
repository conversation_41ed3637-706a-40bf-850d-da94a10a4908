const { defineConfig } = require('@yarnpkg/types')

module.exports = defineConfig({
  async constraints({ Yarn }) {
    // ^ や ~ が付いた依存関係を禁止する
    for (const dep of Yarn.dependencies()) {
      // peerDependencies はスキップ
      if (dep.type === 'peerDependencies') continue

      // workspace:, file:, patch: などの特殊な範囲指定はスキップ
      if (/^(workspace:|file:|patch:|git:|link:)/.test(dep.range)) continue

      // ^ または ~ で始まる場合はエラー
      if (/^[\^~]/.test(dep.range)) {
        dep.error(
          `依存関係 "${dep.ident}" のバージョン "${dep.range}" に ^ または ~ が含まれています。固定バージョンを使用してください。`,
        )
      }
    }
  },
})
